import { functions } from 'src/firebase';
import { httpsCallable } from 'firebase/functions';

export const speechRecognitionTokenFirebase = httpsCallable<
  void,
  { token: string }
>(functions, 'getSpeechRecognitionToken');

export const setPaypalPaymentStatus = httpsCallable<
  { uid: string; paidViaPaypal: boolean },
  void
>(functions, 'setPaypalPayment');

export interface UserWithClaims {
  uid: string;
  email: string;
  displayName: string;
  creationTime: string;
  lastSignInTime: string;
  isAdmin: boolean;
  isReviewer: boolean;
  stripeRole?: string;
  paidViaPaypal: boolean;
}

interface ListUsersRequest {
  /** The maximum number of users to return. */
  page?: number;
  limit?: number;
  /** The page token to use to retrieve the next page of results. */
  pageToken?: string;
  /** A string to filter the users by. The search will be performed on the user's email address and display name. */
  filter?: string;
  /** A string to filter the users by customclaim. The search will be performed on the user's custom claims. */
  filterByRole?: string;
  /** A string to filter the users by customclaim. The search will be performed on the user's custom claims. */
  filterByStatus?: string;
}

interface ListUsersResponse {
  users: UserWithClaims[];
  /** The page token to use to retrieve the next page of results. */
  nextPageToken?: string;
  /** The total number of users matching the filter criteria. */
  totalUsers: number;
}

const listUsersFirebase = httpsCallable<ListUsersRequest, ListUsersResponse>(
  functions,
  'listUsersWithClaims',
);
export async function listUsers(params: ListUsersRequest) {
  const { data } = await listUsersFirebase(params);
  return data;
}

export type OpenAIParams = {
  type: 'compose' | 'simplify';
  prompt: string;
  options?: any;
};

export const requestOpenAIFirebase = httpsCallable<OpenAIParams, string>(
  functions,
  'requestOpenAI',
);

export const setUserAdminStatus = httpsCallable<
  { uid: string; isAdmin: boolean },
  void
>(functions, 'setUserAdminStatus');

export const setUserReviewerStatus = httpsCallable<
  { uid: string; isReviewer: boolean },
  void
>(functions, 'setUserReviewerStatus');

export const cancelStripeSubscriptionFirebase = httpsCallable<
  { subscription: string },
  boolean
>(functions, 'cancelStripeSubscription');

export const cancelPaypalSubscriptionFirebase = httpsCallable<
  { subscription: string },
  boolean
>(functions, 'cancelPaypalSubscription');

export const getSubscriptionInvoicesPaidFirebase = httpsCallable<
  { subscription: string },
  number
>(functions, 'getSubscriptionInvoicesPaid');

export const createStripePortalLinkFirebase = httpsCallable<
  { returnUrl: string; customer: string },
  { url: string }
>(functions, 'ext-firestore-stripe-payments-createPortalLink');

export const requestFetchVoicesFirebase = httpsCallable<
  void,
  {
    name?: string | null;
    languageCodes?: string[] | null;
    ssmlGender?: string;
  }[]
>(functions, 'fetchVoices');

export type TTSParams = {
  text: string;
  voiceName: string;
  languageCode: string;
};
export type TTSParamsLongAudio = {
  text: string;
  voiceName: string;
  languageCode: string;
  storageLocation: string;
  filename: string;
  projectId: string;
};
export const requestConvertTextToSpeech = httpsCallable<TTSParams, string>(
  functions,
  'convertTextToSpeech',
);

export const requestConvertTextToSpeechLongAudio = httpsCallable<
  TTSParamsLongAudio,
  string
>(functions, 'convertTextToSpeechLongAudio');

export const deleteBookRecursively = httpsCallable<{ bookId: string }, void>(
  functions,
  'deleteBookRecursively',
);

export type AmplitudeEvent = {
  user_id: string;
  event_type: string;
  event_properties?: {
    [key: string]: any;
  };
};
export const recordAmplitudeEvent = httpsCallable<AmplitudeEvent, void>(
  functions,
  'recordAmplitudeEvent',
);

export type whisperAiBookChapterAudioParams = {
  audioFileUrl: string;
  dataDoc: string;
  fileName: string;
  prompt: string;
  maxDurationLimit: number;
};

export const requestBookChapterTranscribeAudioFirebase = httpsCallable<
  whisperAiBookChapterAudioParams,
  boolean
>(functions, 'transcribeBookChapterAudioFromSrc', { timeout: 3600000 });

export type ProcessLongTextForAIParams = {
  longText: string;
  docId: string;
};

export const processLongTextForAI = httpsCallable<
  ProcessLongTextForAIParams,
  boolean
>(functions, 'processLongTextForAI');

export type ChatWithMannyParams = {
  query: string;
  docId: string;
  messageHistory?: any[];
  openAiParams?: object;
};

export const chatWithManny = httpsCallable<ChatWithMannyParams, string>(
  functions,
  'chatWithManny',
);
