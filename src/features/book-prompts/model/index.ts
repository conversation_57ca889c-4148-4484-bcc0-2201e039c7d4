import { DialogChainObject } from 'quasar';
export type PromptId = (typeof promptId)[number];
export const promptId = [
  'outline',
  'main',
  'rewrite',
  'addContent',
  'instruct',
  'grammar',
  'simplify',
  'addStory',
] as const;
export interface PromptDetails {
  prompt: string;
  promptId: PromptId;
  promptRequest?: string;
  dialog?: DialogChainObject;
}

export interface Prompt {
  promptId: PromptId;
  title: string;
  prompt: string;
  selectedChapter: string;
  selectedContent?: string;
}
