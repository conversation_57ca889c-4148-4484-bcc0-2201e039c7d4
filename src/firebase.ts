import { initializeApp, type FirebaseOptions } from 'firebase/app';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';
import { getMessaging, onBackgroundMessage } from 'firebase/messaging/sw';
import { onMessage, getToken } from 'firebase/messaging';

const prodConfig: FirebaseOptions = {
  apiKey: 'AIzaSyDkkEKASGfX4zfU9dBS21RVc75ao12DDKY',
  authDomain: 'bspub-3085b.firebaseapp.com',
  databaseURL: 'https://bspub-3085b.firebaseio.com',
  projectId: 'bspub-3085b',
  storageBucket: 'bspub-storage',
  messagingSenderId: '424636372865',
  appId: '1:424636372865:web:3e81269b8db2eab0af3a80',
  measurementId: 'G-HGE6EG62MD',
};

const devConfig: FirebaseOptions = {
  apiKey: 'AIzaSyDd-Xy5zqteDDX9PGBqz4L7NJvB5lWeZBM',
  authDomain: 'manuscriptr-dev.firebaseapp.com',
  projectId: 'manuscriptr-dev',
  storageBucket: 'manuscriptr-dev.appspot.com',
  messagingSenderId: '127339513256',
  appId: '1:127339513256:web:19fd0bbd2c94b28eaac99d',
  measurementId: 'G-10KEXDVMFY',
};

const booksModuleConfig = {
  apiKey: 'AIzaSyBjHfLt-IQnCD9jg18owVFklB6zuM9cnOg',
  authDomain: 'manuscriptr-books-migration.firebaseapp.com',
  projectId: 'manuscriptr-books-migration',
  storageBucket: 'manuscriptr-books-migration.appspot.com',
  messagingSenderId: '842382160482',
  appId: '1:842382160482:web:7e3fa9a1ad564285bd173b',
  measurementId: 'G-JN6J770CCP',
};

let config;
let vapidKey: string;
switch (process.env.FIREBASE_APP) {
  case 'prod': {
    config = prodConfig;
    vapidKey =
      'BAKmP-1qHAb2SfrRXrrDaYIuNTixJ7NrOag5HqZrwKWgQiL60oMXd8NNwaxqK_NxoS-nmEvAwsOKMoon7DmzeoY';
    break;
  }
  case 'books': {
    config = booksModuleConfig;
    vapidKey =
      'BAKmP-1qHAb2SfrRXrrDaYIuNTixJ7NrOag5HqZrwKWgQiL60oMXd8NNwaxqK_NxoS-nmEvAwsOKMoon7DmzeoY';
    break;
  }
  default: {
    config = devConfig;
    vapidKey =
      'BO5TahVh7TyCQPPR6QwdPYmzA9Be5TvkMYju8FzufrkjjXuFs8AYDuvIRmkJLt6K9CE1Uuj3cvbtxmmw75r65uQ';
    break;
  }
}
const siteURL =
  process.env.FIREBASE_APP === 'prod'
    ? 'https://bspub-3085b.firebaseapp.com'
    : 'https://manuscriptr-dev.firebaseapp.com';

const app = initializeApp(config);

// const analytics = getAnalytics(app);
const messaging = getMessaging(app);
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);
const functions = getFunctions(app);

if (location.hostname === 'localhost') {
  // Connect emulators
  // connectAuthEmulator(auth, 'http://localhost:9099');
  // connectFirestoreEmulator(db, 'localhost', 8080);
  // connectFunctionsEmulator(functions, 'localhost', 5001);
}

export {
  app as firebase,
  auth,
  db as firestore,
  storage,
  functions,
  siteURL,
  vapidKey,
  messaging,
  onMessage,
  getToken,
  onBackgroundMessage,
};
