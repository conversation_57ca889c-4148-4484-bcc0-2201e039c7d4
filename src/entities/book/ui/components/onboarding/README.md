# Onboarding Components - Complete UI Design System

This directory contains a comprehensive, modern UI design system for all onboarding components. The system provides a consistent, professional, and mobile-responsive experience across all book creation flows with a unified structure and styling approach.

## 🎨 Design System

### Common SCSS File
- **File**: `onboarding-common.scss`
- **Purpose**: Centralized styling for all onboarding components
- **Features**: 
  - Modern gradient backgrounds
  - Consistent spacing and typography
  - Mobile-responsive design
  - Accessibility support
  - Loading states and animations

### Key Design Elements

#### 1. Header Section
```scss
.onboarding-header
```
- Gradient background with subtle texture
- Robot/Manny avatar integration
- Clear title and subtitle hierarchy
- Auto-save indicators

#### 2. Step Indicators
```scss
.onboarding-steps
```
- Visual progress tracking
- Active, completed, and inactive states
- Mobile-responsive layout

#### 3. Content Areas
```scss
.onboarding-content
```
- Structured form sections
- Icon-enhanced labels
- Consistent spacing

#### 4. Manny Assistant Integration
```scss
.manny-assistant
```
- Consistent robot avatar styling
- Loading animations
- Hover effects

## 🚀 Usage

### 1. Import the Common Styles
Add this to your component's style section:

```scss
<style scoped lang="scss">
@import '../onboarding-common.scss';
// Your component-specific styles here
</style>
```

### 2. Apply Container Classes
Wrap your component content:

```vue
<template>
  <div class="onboarding-content">
    <div class="content-header">
      <div class="content-title">
        <div class="title-icon">
          <q-icon name="your-icon" />
        </div>
        Your Title
      </div>
      <div class="content-subtitle">
        Your subtitle description
      </div>
    </div>
    
    <div class="onboarding-form">
      <!-- Your form content -->
    </div>
  </div>
</template>
```

### 3. Form Sections
Structure your forms consistently:

```vue
<div class="form-section">
  <div class="section-label">
    <div class="label-icon">
      <q-icon name="relevant-icon" />
    </div>
    Section Title
  </div>
  <div class="section-description">
    Brief description of what this section does
  </div>
  <!-- Your form fields -->
</div>
```

### 4. Manny Integration
Add AI assistance buttons:

```vue
<div class="manny-assistant">
  <q-btn
    class="manny-button"
    :loading="loading"
    @click="askManny"
  >
    <div class="manny-avatar" :class="{ loading }">
      <q-img src="robot.png" width="20px" />
    </div>
    Ask Manny
    <q-tooltip>Get AI suggestions</q-tooltip>
  </q-btn>
</div>
```

## 📱 Mobile Responsiveness

The styling system includes comprehensive mobile support:

- **Breakpoints**: 768px (mobile), 1024px (tablet)
- **Responsive grids**: Automatic column adjustment
- **Touch-friendly**: Larger buttons and spacing on mobile
- **Optimized layouts**: Stack elements vertically on small screens

## ♿ Accessibility Features

- **Focus indicators**: Clear outline styles for keyboard navigation
- **High contrast support**: Enhanced visibility in high contrast mode
- **Reduced motion**: Respects user's motion preferences
- **Screen reader friendly**: Proper semantic structure

## 🎭 Available CSS Classes

### Layout Classes
- `.onboarding-container` - Main wrapper
- `.onboarding-header` - Header section
- `.onboarding-content` - Main content area
- `.onboarding-steps` - Step indicator
- `.onboarding-actions` - Action buttons area

### Component Classes
- `.onboarding-form` - Form wrapper
- `.onboarding-chapters` - Chapter management
- `.onboarding-options` - Option groups
- `.manny-assistant` - AI assistant elements

### Utility Classes
- `.onboarding-fade-in` - Fade in animation
- `.onboarding-slide-up` - Slide up animation
- `.onboarding-bounce` - Bounce animation

## 🎨 Color Palette & Themes

### Base Colors
```scss
$onboarding-primary: #00569b;     // Main brand color
$onboarding-secondary: #667eea;   // Secondary accent
$onboarding-accent: #764ba2;      // Gradient accent
$onboarding-success: #4ade80;     // Success states
$onboarding-error: #ef4444;       // Error states
$onboarding-warning: #f59e0b;     // Warning states
```

### Theme-Specific Colors

#### Faith Story Theme
- **Primary**: Purple gradients (#8b5cf6 to #a855f7)
- **Icons**: Church, favorite, auto_awesome
- **Feel**: Spiritual, hopeful, transformative

#### Autobiography Theme
- **Primary**: Amber gradients (#f59e0b to #d97706)
- **Icons**: Person_book, star, account_circle
- **Feel**: Personal, warm, reflective

#### Non-Fiction Theme
- **Primary**: Blue gradients (#00569b to #667eea)
- **Icons**: Psychology, edit, list_alt
- **Feel**: Professional, authoritative, educational

## 📁 Updated Components

The following components have been updated with the new styling:

### ✅ Complete UI Design System - All Components Redesigned!

#### 🎨 **Modern Design Structure**
All components now follow a consistent, professional structure:
- **Unified Layout**: `onboarding-step` → `step-container` → `onboarding-content` → `content-body`
- **Enhanced Headers**: Large icons, clear titles, descriptive subtitles
- **Professional Cards**: White content cards with shadows and rounded corners
- **Consistent Spacing**: Systematic spacing using design tokens

#### 📱 **Non-Fiction Components**
- `TitleStep.vue` - Professional title creation with enhanced input styling
- `ChaptersStep.vue` - Modern chapter management with drag-and-drop
- `MissionStep.vue` - Comprehensive mission builder (existing)
- `NewBookFlow.vue` - Enhanced header with "Non-Fiction Book Foundations"
- `TitleSubtitleStep.vue` - Dual-column layout with AI suggestions

#### ⛪ **Faith Story Components**
- `FaithStoryTitleStep.vue` - Purple-themed with spiritual iconography
- `FaithStoryChaptersStep.vue` - Faith journey chapter organization
- `NewFaithStoryFlow.vue` - "Faith Story Foundations" with church icon
- `FaithStoryTitleSubtitleStep.vue` - Spiritual title & subtitle creation

#### 📖 **Autobiography Components**
- `AutoBioTitleStep.vue` - Amber-themed memoir title creation
- `AutoBioChaptersStep.vue` - Life story chapter management
- `NewAutoBioFlow.vue` - "Autobiography Foundations" with memoir styling
- `AutoBioTitleSubtitleStep.vue` - Personal story title & subtitle creation

### 🎯 **Comprehensive Design System**
- **1,600+ lines** of consolidated SCSS in `onboarding-common.scss`
- **Global Import**: Styles available throughout app via `src/css/app.scss`
- **Theme System**: Automatic theming with `faith-story-theme`, `autobiography-theme`
- **Zero Duplication**: All repetitive styles eliminated
- **Enhanced Components**: Larger inputs, better buttons, professional spacing

### 🔄 Remaining Components (Optional)
- `SubtitleStep.vue` (non-fiction)
- `FaithStorySubtitleStep.vue`
- `FaithStoryMissionStep.vue`
- `AutoBioSubtitleStep.vue`
- `AutoBioMissionStep.vue`

## 🛠 Customization

### Component-Specific Styles
Add component-specific styles after importing the common styles:

```scss
<style scoped lang="scss">
@import '../onboarding-common.scss';

// Your custom styles
.my-custom-element {
  // Custom styling that extends the base system
}
</style>
```

### Overriding Variables
To customize colors or spacing, override variables before importing:

```scss
<style scoped lang="scss">
// Override variables
$onboarding-primary: #your-color;

@import '../onboarding-common.scss';
</style>
```

## 🚀 Next Steps

1. **Apply to remaining components**: Update all onboarding components to use the new system
2. **Test across devices**: Ensure consistent experience on all screen sizes
3. **Accessibility audit**: Verify all accessibility features work correctly
4. **Performance optimization**: Minimize CSS bundle size
5. **Documentation**: Create component-specific usage examples

## 💡 Best Practices

1. **Consistent Icons**: Use meaningful icons from the Quasar icon set
2. **Progressive Enhancement**: Ensure functionality without JavaScript
3. **Loading States**: Always provide feedback during async operations
4. **Error Handling**: Use consistent error styling and messaging
5. **User Feedback**: Implement tooltips and helpful descriptions

This styling system provides a solid foundation for creating beautiful, consistent, and accessible onboarding experiences across all book creation flows.
