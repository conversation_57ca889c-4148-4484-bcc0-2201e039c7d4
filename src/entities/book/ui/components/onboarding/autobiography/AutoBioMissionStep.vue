<template>
  <OnboardingStepWrapper
    theme="autobiography"
    content-title="Write your Autobiography Book"
    content-subtitle="Share your life story and inspire others with your unique journey and experiences."
    content-icon="auto_stories"
    :show-actions="false"
  >
    <OnboardingFormSection
      label="Story Purpose"
      description="Tell us about your motivation and target audience for your autobiography."
      icon="psychology"
      class="q-gutter-sm"
    >
      <OnboardingInput
        v-model="mainReason"
        label="What is the main reason you want to share your story?"
        type="textarea"
        :rows="3"
        placeholder="Share your motivation for writing your autobiography..."
        append-icon="mic"
        @append-click="openTranscription('mainReason')"
      />

      <OnboardingOptionGrid
        :options="bookFocusedOptions"
        v-model:selected-value="bookFocused"
        :columns="2"
      />

      <OnboardingOptionGrid
        :options="bookOrderOptions"
        v-model:selected-value="bookOrder"
        :columns="2"
      />
    </OnboardingFormSection>

    <OnboardingFormSection
      label="Target Audience"
      description="Define who you want to reach with your story."
      icon="people"
      class="q-gutter-sm"
    >
      <div class="form-grid grid-2">
        <OnboardingInput
          v-model="gender"
          label="Target audience gender"
          type="select"
          :options="['Male', 'Female', 'Both']"
        />

        <OnboardingInput
          v-model="age"
          label="Target audience age"
          type="select"
          :options="ageOptions"
          multiple
          use-chips
        />
      </div>

      <OnboardingInput
        v-model="description"
        label="Describe your target audience in a few words"
        type="textarea"
        :rows="2"
        placeholder="e.g., Working mothers, young entrepreneurs, people overcoming adversity..."
        append-icon="mic"
        @append-click="openTranscription('description')"
      />
    </OnboardingFormSection>

    <OnboardingFormSection
      label="Key Message"
      description="What's the main takeaway you want readers to get from your book?"
      icon="message"
    >
      <OnboardingInput
        v-model="keyMessage"
        label="Key message or takeaway"
        type="textarea"
        :rows="3"
        placeholder="What transformation or insight do you want to share?"
        :loading="loadingKeyMessage"
        :disabled="loadingKeyMessage"
      >
        <template #append>
          <q-btn
            flat
            icon="mic"
            @click="openTranscription('keyMessage')"
            :disable="loadingKeyMessage"
          >
            <q-tooltip>Transcribe</q-tooltip>
          </q-btn>
          <MannyButton
            variant="round"
            :loading="loadingKeyMessage"
            :disabled="loadingKeyMessage || !canGenerateKeyMessage"
            tooltip="Ask Manny for help with your key message"
            @click="generateKeyMessage(false)"
          />
        </template>
      </OnboardingInput>
    </OnboardingFormSection>
  </OnboardingStepWrapper>

  <q-dialog v-model="transcriptionDialog" persistent>
    <BookTranscriptionApp
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useVModels, toRefs } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';
import {
  OnboardingStepWrapper,
  OnboardingFormSection,
  OnboardingInput,
  OnboardingOptionGrid,
  MannyButton,
  type OnboardingOption,
} from '../shared';

const transcriptionDialog = ref(false);

export interface Questions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  keyMessage: string;
  mainReason: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
}

const props = withDefaults(
  defineProps<{
    questions: Questions;
    updating?: boolean;
    isScreenBiggerMd?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:questions': [value: Questions];
}>();
const { questions } = useVModels(props, emit);
const {
  gender,
  age,
  description,
  keyMessage,
  mainReason,
  bookOrder,
  bookFocused,
} = toRefs(questions);

const $q = useQuasar();

const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

enum Modes {
  Manual,
  Help,
}
const selectedField = ref('');
const mode = ref(Modes.Manual);
const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'keyMessage':
      keyMessage.value = content;
      break;
    case 'description':
      description.value = content;
      break;
    case 'mainReason':
      mainReason.value = content;
      break;
  }
  transcriptionDialog.value = false;
};
const ageOptions = [
  'Any Age',
  'Under 18',
  '18-25',
  '25-35',
  '35-50',
  '50-65',
  '65+',
];

const bookOrderOptions: OnboardingOption[] = [
  {
    label: 'Chronological',
    value: 'Chronological',
    description: 'Tell your story in order of life events',
    icon: 'timeline',
  },
  {
    label: 'Thematic',
    value: 'Thematic',
    description: 'Organize by lessons, challenges, and turning points',
    icon: 'category',
  },
];

const bookFocusedOptions: OnboardingOption[] = [
  {
    label: 'Legacy-focused',
    value: 'Legacy-focused',
    description: 'Preserve your story for future generations',
    icon: 'family_history',
  },
  {
    label: 'Inspirational',
    value: 'Inspirational',
    description: 'Motivate and encourage others',
    icon: 'lightbulb',
  },
  {
    label: 'Educational',
    value: 'Educational',
    description: 'Teach lessons from your experiences',
    icon: 'school',
  },
  {
    label: 'Transformational',
    value: 'Transformational',
    description: 'Help others change their lives',
    icon: 'transform',
  },
];

// Computed properties
const canGenerateKeyMessage = computed(() => {
  return gender.value && age.value.length > 0 && description.value;
});

const openTranscription = (field: string) => {
  selectedField.value = field;
  transcriptionDialog.value = true;
};

const loadingKeyMessage = ref(false);
async function generateKeyMessage(invoking: boolean = false) {
  if (
    (!gender.value || age.value.length === 0 || !description.value) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.keyMessage;
  if (keyMessage.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.keyMessage =
      typeof results.value.keyMessage === 'string'
        ? results.value.keyMessage
        : await results.value.keyMessage;
  }

  if (!invoking) loadingKeyMessage.value = true;
  if (results.value.keyMessage) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyMessage}`;
  }
  try {
    const response = await composeText(promptRequest);
    if (!invoking) keyMessage.value = response;
    // save the results
    results.value.keyMessage = `${results.value.keyMessage} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingKeyMessage.value = false;
  }
}

const ageGroups = computed(() => ageConcat.format(age.value));

// Updating of Open AI Prompt
const results = ref({} as { [key: string]: any });

const promptKeyMessage = computed(
  () => `
  <p>Main Reason: ${questions.value.mainReason}</p>
  <p>Book Structure: ${questions.value.bookOrder}</p>
  <p>Book Focused: ${questions.value.bookFocused}</p>
  <br/>
  You are an award-winning ghostwriter with decades of experience writing bestselling autobiographies and memoirs. Your specialty is helping new authors discover the heart of their story—the message that will resonate most deeply with readers.
  <br/>
  Based on the following details, write a single, emotionally compelling key message or takeaway that readers should remember after finishing this book:
  <ul>
  <li>Main reason for writing: ${mainReason.value}</li>
  <li>Book focus: ${bookFocused.value}</li>
  <li>Book structure: ${bookOrder.value}</li>
  </ul>
  Respond with 1–2 emotionally impactful sentences. The response should feel like a back cover hook or a central theme. Do not include bullet points, labels, or any formatting—just the raw text.
`,
);

onMounted(async () => {
  if (age.value.length) {
    mode.value = Modes.Help;
  }
  if (keyMessage.value) {
    try {
      await generateKeyMessage(true);
    } catch (e) {
      console.error(e);
    }
  }
});

const updatedPrompt = computed(() => ({
  keyMessage: promptKeyMessage.value,
}));
</script>

<style scoped></style>
