<template>
  <q-dialog v-model="onBoardingActive" persistent>
    <q-card
      :style="{
        maxWidth: '100vw',
        width: $q.screen.gt.xs ? '60vw' : '100%',
        minHeight: '90%',
      }"
    >
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Book Foundations</div>

          <q-space />
          <div v-if="autosave === true" class="q-mr-md">
            Auto-saving <q-spinner-dots color="white" size="0.7em" />
            <q-tooltip :offset="[0, 8]">Auto save feature on</q-tooltip>
          </div>
          <div v-else class="q-mr-xs">
            <q-btn
              dense
              flat
              icon="save"
              @click="complete"
              label="Click here to save"
              color="white"
            >
              <q-tooltip class="bg-white text-primary"
                >Save your progress</q-tooltip
              >
            </q-btn>
          </div>

          <q-btn
            dense
            flat
            icon="close"
            @click="cancel"
            v-if="autosave === true"
          >
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-stepper
        v-model="step"
        header-nav
        animated
        class="new-book-dialog"
        :contracted="$q.screen.lt.sm"
      >
        <!-- Step 1 - Mission statement creation -->
        <q-step
          :name="Steps.CreateMission"
          title="Mission Statement"
          icon="create_new_folder"
          :done="mission.length > 0"
        >
          <FaithStoryMissionStep
            :updating="updating"
            :isScreenBiggerMd="isScreenBiggerMd"
            v-model:questions="missionQuestions"
          />
          <q-separator />
          <q-card-section v-if="isScreenBiggerMd">
            <q-card-actions align="right">
              <q-btn
                @click="step = Steps.WriteTitle"
                color="primary"
                label="Next"
                icon-right="arrow_forward"
                outline
              />
            </q-card-actions>
          </q-card-section>
        </q-step>

        <!-- Step 2 - Choose a title -->
        <q-step
          :name="Steps.WriteTitle"
          title="Title"
          icon="create_new_folder"
          :done="title.length > 0 && subtitle.length > 0"
        >
          <q-scroll-area
            :thumb-style="thumbStyle"
            :bar-style="barStyle"
            :style="{
              height: `calc(100vh - ${isScreenBiggerMd ? '320px' : '210px'})`,
            }"
          >
            <q-card-section>
              <div class="q-gutter-md">
                <FaithStoryTitleSubtitleStep
                  v-model:titleModelValue="title"
                  v-model:subtitleModelValue="subtitle"
                  v-model:questions="missionQuestions"
                  :updating="updating"
                />
              </div>
            </q-card-section>
          </q-scroll-area>

          <q-separator />
          <q-card-section v-if="isScreenBiggerMd">
            <q-card-actions align="right">
              <q-btn
                @click="step = Steps.Recap"
                color="danger"
                label="Skip"
                icon-right="close"
                outline
              >
                <q-tooltip>Skip to Recap</q-tooltip>
              </q-btn>
              <q-btn
                @click="step = Steps.Chapters"
                color="primary"
                label="Next"
                icon-right="arrow_forward"
                outline
              />
            </q-card-actions>
          </q-card-section>
        </q-step>

        <!-- Step 3 - Chapters -->
        <q-step
          :name="Steps.Chapters"
          title="Chapters"
          icon="create_new_folder"
          :done="chapters.length > 0"
        >
          <FaithStoryChaptersStep
            v-model="chapters"
            :title="title"
            :subtitle="subtitle"
            :isScreenBiggerMd="isScreenBiggerMd"
            v-model:autosave="autosave"
            v-model:questions="missionQuestions"
            @complete="complete"
          />
          <q-separator />
          <q-card-section v-if="isScreenBiggerMd">
            <q-card-actions align="right">
              <q-btn
                @click="step = Steps.Recap"
                color="primary"
                label="Next"
                icon-right="arrow_forward"
                outline
              />
              <q-btn
                @click="complete"
                color="primary"
                outline
                label="Save"
                icon-right="check"
              >
                <q-tooltip>Save Book Foundations?</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card-section>
        </q-step>

        <!-- Step 4 - Recap -->
        <q-step :name="Steps.Recap" title="Recap" icon="create_new_folder">
          <q-card-section class="q-pa-none">
            <q-card class="q-pa-none no-box-shadow no-border">
              <q-card-section class="flex justify-between">
                <div class="text-h7">
                  Here's a recap of
                  {{ updating ? 'your updates' : "what we're starting with" }}!
                  <div class="text-subtitle2">
                    These are all editable during writing
                  </div>
                </div>

                <div v-if="!isScreenBiggerMd" class="q-gutter-sm">
                  <q-btn @click="cancel" color="danger" outline label="Cancel">
                    <q-tooltip>Do not save changes.</q-tooltip>
                  </q-btn>
                  <q-btn @click="complete" color="primary" outline label="Save">
                    <q-tooltip>Save Book Foundations?</q-tooltip>
                  </q-btn>
                </div>
              </q-card-section>
              <q-separator />
              <q-scroll-area
                :thumb-style="thumbStyle"
                :bar-style="barStyle"
                :style="{
                  height: `calc(100vh - ${
                    isScreenBiggerMd ? '400px' : '260px'
                  })`,
                }"
              >
                <q-card-section class="q-py-none">
                  <p class="text-h6">
                    <q-btn
                      round
                      flat
                      icon="edit"
                      @click="step = Steps.WriteTitle"
                    />
                    Working title:
                    <span class="text-italic" v-text="title"></span>
                  </p>
                </q-card-section>
                <q-card-section class="q-py-none">
                  <p class="text-h6">
                    <q-btn
                      round
                      flat
                      icon="edit"
                      @click="step = Steps.WriteTitle"
                    />
                    Working subtitle:
                    <span class="text-italic" v-text="subtitle"></span>
                  </p>
                </q-card-section>

                <q-card-section class="q-pt-none">
                  <p class="text-h6">
                    <q-btn
                      round
                      flat
                      icon="edit"
                      @click="step = Steps.Chapters"
                    />
                    Chapter outline:
                  </p>
                  <ol>
                    <li v-for="chapter in chapters" class="text-subtitle2">
                      {{ chapter.title }}
                    </li>
                  </ol>
                </q-card-section>
              </q-scroll-area>
            </q-card>
          </q-card-section>
          <q-separator />
          <q-card-section v-if="isScreenBiggerMd">
            <q-card-actions align="right">
              <q-btn
                @click="cancel"
                color="danger"
                outline
                label="Cancel"
                icon-right="close"
              >
                <q-tooltip>Do not save changes.</q-tooltip>
              </q-btn>
              <q-btn
                @click="complete"
                color="primary"
                outline
                label="Save"
                icon-right="check"
              >
                <q-tooltip>Save Book Foundations?</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card-section>
        </q-step>
      </q-stepper>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  onUnmounted,
  ref,
  toRef,
  unref,
  watch,
} from 'vue';

import FaithStoryMissionStep, {
  type Questions as MissionQuestions,
} from './FaithStoryMissionStep.vue';
import FaithStoryTitleSubtitleStep from './FaithStoryTitleSubtitleStep.vue';
import FaithStoryChaptersStep from './FaithStoryChaptersStep.vue';

import {
  Book,
  NewBook,
  ChapterOutline,
  FaithStoryMissionQuestions,
} from 'src/entities/book/model/types';
import { listOutlines, setBook, updateBook } from 'src/entities/book';

import { barStyle, thumbStyle } from 'src/entities/setting';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import { useQuasar } from 'quasar';

import { ACTIONS, loggingService, PAGES } from 'src/entities/log';
import { useVModel, watchDebounced } from '@vueuse/core';
import { useRouter } from 'vue-router';
import TitleSubtitleStep from '../nonfiction/TitleSubtitleStep.vue';

export type NewBookStarter = Pick<
  NewBook,
  'title' | 'subtitle' | 'mission' | 'missionQuestions' | 'chapters'
>;

export type ExistingBookStarter = Book;
export type ExistingBookOutlinesStarter = ChapterOutline[];

const props = defineProps<{
  modal: boolean;
  initialValues?: ExistingBookStarter;
  initialOutlines?: ExistingBookOutlinesStarter;
  shouldUpdate?: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  completed: [book: NewBookStarter];
  updated: [id: string, book: ExistingBookStarter];
}>();

const onBoardingActive = ref(props.modal);
const router = useRouter();
// Reactive references for browser width and div size
const browserWidth = ref(window.innerHeight);
const isScreenBiggerMd = computed(
  () => $q.screen.gt.sm && browserWidth.value > 650,
);
// Function to update the browser width
const updateWidth = () => {
  browserWidth.value = window.innerHeight;
};
const $q = useQuasar();

onMounted(() => {
  // Update browser width on resize
  window.addEventListener('resize', updateWidth);
});

onUnmounted(() => {
  // Clean up event listeners and observers
  window.removeEventListener('resize', updateWidth);
});

enum Steps {
  CreateMission,
  WriteTitle,
  Chapters,
  Recap,
}

const step = ref<Steps>(Steps.CreateMission);
const initialValues = toRef(props, 'initialValues');
const initialOutlines = toRef(props, 'initialOutlines');
const shouldUpdate = toRef(props, 'shouldUpdate');
// const autosave = useVModel(props, 'autosave', emit);
const autosave = ref(true);

const updating = computed(() => initialValues.value != undefined);
const missionQuestions = ref<FaithStoryMissionQuestions>(
  (initialValues.value?.missionQuestions as FaithStoryMissionQuestions) ?? {
    gender: 'Both',
    age: [],
    description: '',
    bestDescription: 'A personal testimony of coming to faith',
    otherBestDescription: '',
    keyLifeEvents: '',
    readersTakeaway: '',
    mainReason: '',
    bookOrder: 'Chronological',
    bookFocused: '',
    authorImpact: '',
  },
);

const mission = ref(initialValues.value?.mission ?? '');
const title = ref(initialValues.value?.title ?? '');
const subtitle = ref(initialValues.value?.subtitle ?? '');

let chaptersList = [];

if (!initialOutlines.value?.length) {
  const outlines = initialValues.value?.id
    ? unref(await listOutlines(initialValues.value?.id))
    : null;

  if (outlines?.length && outlines?.outlines?.length) {
    // autosave.value = false;
    chaptersList = outlines?.outlines;
  } else {
    // autosave.value = true;
    chaptersList = initialValues.value?.chapters || [];
  }
} else {
  chaptersList = initialOutlines.value;
}

const chapters = ref<any[]>(chaptersList);

watch(
  step,
  async (selectedStep) => {
    let selected = 'Mission statement';
    let aSave = true;
    switch (selectedStep) {
      case 1: {
        selected = 'Title and Subtitle';
        break;
      }
      case 2: {
        selected = 'Chapters';
        aSave = true;
        break;
      }
      case 3: {
        selected = 'Recap';
        aSave = false;
        break;
      }
      default: {
        selected = 'Mission statement';
        break;
      }
    }
    autosave.value = aSave;

    await loggingService.logAction(
      PAGES.BOOKMISSIONSTATEMENT,
      ACTIONS.CLICK,
      `Step in Book Foundation is selected: ${selected}`,
      MODULE_NAME,
      initialValues.value?.id as string,
    );
  },
  { immediate: true },
);
watch(
  props,
  (newVal) => {
    onBoardingActive.value = newVal.modal as boolean;
  },
  { immediate: true },
);
// Define the module or component name
const MODULE_NAME = 'book_foundations';

const cancel = () => {
  if (shouldUpdate.value === true) {
    emit('updated', initialValues.value?.id, {
      ...initialValues.value,
      id: initialValues.value?.id,
      mission: mission.value,
      title: title.value,
      subtitle: subtitle.value,
      chapters: chapters.value,
      missionQuestions: missionQuestions.value,
    });
  } else {
    emit('cancel');
  }
  if (onBoardingActive.value === true) {
    onBoardingActive.value = false;
  }
};
async function complete() {
  if (initialValues.value && initialValues.value?.id) {
    const shouldSave = await confirmOverrideText($q, {
      message: `Are you sure you want to save the changes?`,
    });
    if (!shouldSave) {
      return;
    }
  }

  // TypeScript complains when it is one function calls and the event name is factored out
  if (initialValues.value === undefined) {
    emit('completed', {
      mission: mission.value,
      title: title.value,
      subtitle: subtitle.value,
      chapters: chapters.value,
      missionQuestions: missionQuestions.value,
    });
  } else {
    emit('updated', initialValues.value?.id, {
      ...initialValues.value,
      id: initialValues.value?.id,
      mission: mission.value,
      title: title.value,
      subtitle: subtitle.value,
      chapters: chapters.value,
      missionQuestions: missionQuestions.value,
    });

    await loggingService.logAction(
      PAGES.BOOKMISSIONSTATEMENT,
      ACTIONS.UPDATE,
      `Book is updated from the mission statement: ${
        title.value || 'Untitled book'
      }`,
      MODULE_NAME,
      initialValues.value?.id as string,
    );

    if (autosave.value === true && book.value?.id) {
      await router.push(`/books/${book.value.id}`);
    }
  }
  if (onBoardingActive.value === true) {
    onBoardingActive.value = false;
  }
}

const book = computed(() => {
  const updatedBook = {
    ...initialValues.value,
    id: initialValues.value?.id,
    mission: mission.value,
    title: title.value,
    subtitle: subtitle.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };
  return updatedBook;
});
// handle updates on book object
watchDebounced(
  book,
  (newBook) => {
    if (autosave.value === true) {
      setBook(book.value.id, newBook);
    }
  },
  { debounce: 500 },
);
// handle updates on book chapters
watchDebounced(
  chapters,
  (newChapters) => {
    if (autosave.value === true) {
      book.value.chapters = newChapters;
      updateBook(book.value.id, book.value);
    }
  },
  { debounce: 500, deep: true },
);
</script>

<style>
.new-book-dialog {
  width: 100%;
  font-size: 1.125rem;
  box-shadow: none;
}
.new-book-dialog .q-stepper__title {
  font-size: 1.25rem;
  font-weight: 500;
}
.q-stepper--horizontal .q-stepper__step-inner {
  padding: 0;
}
</style>
