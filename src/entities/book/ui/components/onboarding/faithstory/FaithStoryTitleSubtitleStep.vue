<template>
  <div class="q-gutter-md">
    <!-- Header Section -->
    <div class="row items-center q-mb-md">
      <div class="text-h7 col">Edit your title or let AI help</div>
      <q-icon name="img:robot.png" size="sm" class="q-ml-sm" />
    </div>

    <!-- Input Section -->
    <div class="q-gutter-md">
      <q-input
        outlined
        v-model="title"
        label="Title"
        class="full-width"
        :disable="loading"
      />

      <q-input
        outlined
        v-model="subtitle"
        label="Subtitle"
        class="full-width"
        :disable="loading"
      />

      <!-- Single Manny Button -->
      <div class="row justify-center q-mt-md">
        <q-btn
          color="primary"
          outline
          :loading="loading"
          :disable="loading || !canGenerate"
          @click="generateTitleSubtitlePairs"
          class="q-px-lg"
        >
          <q-icon name="img:robot.png" class="q-mr-sm" />
          Generate Title & Subtitle Ideas
          <q-tooltip>Ask <PERSON> for suggestions</q-tooltip>
        </q-btn>
      </div>
    </div>

    <!-- Results Section - Two Column Layout -->
    <div class="row q-gutter-md" v-if="suggestions.titles.length > 0">
      <!-- Left Column - Titles -->
      <div class="col">
        <div class="text-h6 q-mb-sm">Title Suggestions</div>
        <q-list bordered separator class="rounded-borders">
          <q-item
            v-for="(titleOption, index) in titleOptions"
            :key="'title-' + index"
            clickable
            v-ripple
            @click="title = titleOption.value"
            :class="{ 'bg-blue-1': title === titleOption.value }"
          >
            <q-item-section>
              <q-item-label>{{ titleOption.label }}</q-item-label>
            </q-item-section>
            <q-item-section side v-if="title === titleOption.value">
              <q-icon name="check" color="primary" />
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- Right Column - Subtitles -->
      <div class="col">
        <div class="text-h6 q-mb-sm">Subtitle Suggestions</div>
        <q-list bordered separator class="rounded-borders">
          <q-item
            v-for="(subtitleOption, index) in subtitleOptions"
            :key="'subtitle-' + index"
            clickable
            v-ripple
            @click="subtitle = subtitleOption.value"
            :class="{ 'bg-blue-1': subtitle === subtitleOption.value }"
          >
            <q-item-section>
              <q-item-label>{{ subtitleOption.label }}</q-item-label>
            </q-item-section>
            <q-item-section side v-if="subtitle === subtitleOption.value">
              <q-icon name="check" color="primary" />
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useVModel, useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { FaithStoryMissionQuestions } from 'src/entities/book/model/types';

const props = withDefaults(
  defineProps<{
    titleModelValue: string;
    subtitleModelValue: string;
    questions?: FaithStoryMissionQuestions;
    updating?: boolean;
  }>(),
  {
    updating: false,
  },
);

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);
const $q = useQuasar();

// Component state
const loading = ref(false);
const suggestions = ref({
  titles: [] as string[],
  subtitles: [] as string[],
});
const results = ref(''); // Store previous results to avoid repetition
const questions = useVModel(props, 'questions', emit);

// Computed properties
const canGenerate = computed(() => {
  return (
    questions?.value?.mainReason &&
    (questions?.value?.otherBestDescription ||
      questions?.value?.bestDescription) &&
    questions?.value?.bookOrder &&
    questions?.value?.bookFocused &&
    questions?.value?.description &&
    questions?.value?.keyLifeEvents &&
    questions?.value?.readersTakeaway &&
    questions?.value?.authorImpact
  );
});

const titleOptions = computed(() => {
  return suggestions.value.titles.map((title, index) => ({
    label: title,
    value: title,
    index,
  }));
});

const subtitleOptions = computed(() => {
  return suggestions.value.subtitles.map((subtitle, index) => ({
    label: subtitle,
    value: subtitle,
    index,
  }));
});

// AI prompt for combined title-subtitle generation
const combinedPrompt = computed(() => {
  let promptStr = `Generate 5 title and subtitle pairs for a faith-based book.

  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Reader Takeaway: ${questions?.value?.readersTakeaway}</p>
  <p>Book Goals: ${questions?.value?.authorImpact}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>`;

  promptStr += `

Requirements:
1. Create exactly 5 title-subtitle pairs
2. Titles should be 1 or 3 words (never 2 words), avoiding gerunds
3. Subtitles should include promises or benefits, be compelling and original
4. Each title should be on its own line, followed by its subtitle on the next line
5. Separate each pair with a blank line
6. Use plain text without numbering, bullets, or special formatting
7. Make titles provocative and curiosity-inducing
8. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;

  return promptStr;
});

// Generate title-subtitle pairs
async function generateTitleSubtitlePairs() {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message:
        'Manny needs your mission statement and core needs or transformations to generate suggestions.',
    });
    return;
  }

  // Check if user wants to override existing content
  if (
    (title.value || subtitle.value) &&
    suggestions.value.titles.length === 0
  ) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return;
    }
  }

  let promptRequest = combinedPrompt.value;

  // Add previous results to avoid repetition
  if (results.value) {
    promptRequest += `\n\nPlease avoid repeating or being similar to these previous results:\n${results.value}`;
  }

  loading.value = true;

  try {
    const response = await composeText(promptRequest);

    // Parse the response to extract titles and subtitles
    const pairs = parseTitleSubtitleResponse(response);

    if (pairs.titles.length === 5 && pairs.subtitles.length === 5) {
      suggestions.value = pairs;
      // Store results to avoid repetition in future calls
      results.value = `${results.value}\nTitles: ${pairs.titles.join(
        ', ',
      )}\nSubtitles: ${pairs.subtitles.join(', ')}`;
    } else {
      throw new Error('Unexpected response format from AI');
    }
  } catch (e) {
    console.error(e);
    $q.notify({
      type: 'negative',
      message:
        'An error occurred while generating suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

// Parse AI response to extract title-subtitle pairs
function parseTitleSubtitleResponse(response: string): {
  titles: string[];
  subtitles: string[];
} {
  const titles: string[] = [];
  const subtitles: string[] = [];

  // Split by double newlines to get pairs, then by single newlines within pairs
  const lines = response
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      const title = lines[i]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();
      const subtitle = lines[i + 1]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();

      if (title && subtitle) {
        titles.push(title);
        subtitles.push(subtitle);
      }
    }
  }

  return { titles, subtitles };
}

// Initialize component
onMounted(async () => {
  // If there are existing values, don't auto-generate
  if (title.value || subtitle.value) {
    // Could optionally generate suggestions in background
  }
});
</script>

<style scoped>
.title-subtitle-step {
  max-width: 100%;
}

.q-list .q-item {
  min-height: 56px;
}

.q-item-label {
  white-space: normal;
  word-break: break-word;
}

@media (max-width: 768px) {
  .row.q-gutter-md > .col {
    margin-bottom: 1rem;
  }
}
</style>
