<template>
  <div class="onboarding-step faith-story-theme">
    <div class="step-container">
      <div class="onboarding-content">
        <div class="content-header">
          <div class="content-title">
            <div class="title-icon">
              <q-icon name="menu_book" />
            </div>
            Faith Story Chapter Outline
          </div>
          <div class="content-subtitle">
            Structure your faith journey with meaningful chapters that guide
            readers through your spiritual transformation.
          </div>
        </div>

        <div class="content-body">
          <div class="onboarding-form">
            <div class="form-section">
              <div class="section-label">
                <div class="label-icon">
                  <q-icon name="church" />
                </div>
                Faith Journey Chapters
              </div>
              <div class="section-description">
                Organize your spiritual story into chapters that show your
                transformation and inspire others on their faith journey.
              </div>

              <div class="form-actions">
                <q-btn
                  class="action-btn btn-secondary"
                  :disable="loadingChapters"
                  @click="addNewChapter"
                  icon="add"
                  label="Add Chapter"
                  outline
                >
                  <q-tooltip>Add a new chapter</q-tooltip>
                </q-btn>

                <div class="manny-assistant">
                  <q-btn
                    class="manny-button"
                    @click="generateChapters(false)"
                    :disable="loadingChapters"
                    :loading="loadingChapters"
                  >
                    <div
                      class="manny-avatar"
                      :class="{ loading: loadingChapters }"
                    >
                      <q-img src="robot.png" width="20px" />
                    </div>
                    Ask Manny for Faith Chapters
                    <q-tooltip
                      >Let Manny generate faith-based chapter
                      suggestions</q-tooltip
                    >
                  </q-btn>
                </div>

                <q-btn
                  @click="$emit('complete')"
                  class="action-btn btn-primary"
                  label="Save Progress"
                  icon="save"
                  v-if="!isScreenBiggerMd"
                >
                  <q-tooltip>Save Book Foundations</q-tooltip>
                </q-btn>
              </div>
            </div>

            <div class="chapter-list" v-if="chapters.length">
              <draggable
                v-model="chapters"
                item-key="number"
                class="chapter-draggable"
              >
                <template #item="{ element: chapter, index }">
                  <div class="chapter-item onboarding-fade-in">
                    <div class="chapter-header">
                      <div class="drag-handle">
                        <q-icon name="drag_indicator" />
                      </div>
                      <div class="chapter-number">{{ index + 1 }}</div>
                      <div class="chapter-content">
                        <q-input
                          v-model="chapters[index].title"
                          :loading="loadingChapters"
                          :disable="loadingChapters"
                          outlined
                          :placeholder="`Chapter ${
                            index + 1
                          } - Your faith journey step...`"
                          class="chapter-input"
                        />
                      </div>
                      <div class="chapter-actions">
                        <q-btn
                          icon="delete"
                          class="action-btn delete-btn"
                          flat
                          round
                          :disable="loadingChapters"
                          @click="deleteChapter(index)"
                        >
                          <q-tooltip>Delete Chapter</q-tooltip>
                        </q-btn>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>

            <div
              class="add-chapter-btn"
              @click="addNewChapter"
              v-if="!chapters.length || chapters.length < 12"
            >
              <q-icon name="add" class="add-icon" />
              <span>Add your first faith chapter</span>
            </div>

            <div class="onboarding-loading" v-if="loadingChapters">
              <div class="loading-spinner">
                <div class="spinner"></div>
              </div>
              <div class="loading-text">
                Manny is generating faith-based chapter suggestions...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import draggable from 'vuedraggable';
import {
  confirmOverrideText,
  showError,
  warn,
} from 'src/shared/lib/quasar-dialogs';
import { composeText } from 'src/shared/api/openai';
import type { NewChapterOutline } from 'src/entities/book';
import { barStyle, thumbStyle } from 'src/entities/setting';
import { FaithStoryMissionQuestions } from 'src/entities/book/model/types';

const props = defineProps<{
  modelValue: NewChapterOutline[];
  title: string;
  subtitle: string;
  questions?: FaithStoryMissionQuestions;
  isScreenBiggerMd?: boolean;
}>();
const emit = defineEmits<{
  'update:modelValue': [value: string[]];
  complete: [];
}>();

const chapters = useVModel(props, 'modelValue', emit);
const questions = useVModel(props, 'questions', emit);

const $q = useQuasar();

const loadingChapters = ref(false);

const addNewChapter = () => {
  chapters.value.push({
    title: '',
    outline: '',
    wordCount: 0,
    number: chapters.value.length,
    isSection: false,
  });
};

const deleteChapter = async (index: number) => {
  const shouldDelete = await confirmOverrideText($q, {
    message: `Are you sure you want to delete this faith chapter?`,
  });
  if (!shouldDelete) {
    return;
  }
  chapters.value.splice(index, 1);
};
const prompt = computed(() => {
  return `Using the book title and subtitle provided below, generate 9 chapter title outlines using the following criteria:
  <ol>
    <li>Provide a numbered list. For example: "1. Chasing Shadows in the Rain"</li>
    <li>Each chapter title should be 4-6 words, based on the emotional tone, key events, or personal themes suggested by the title, subtitle, and the author's answers.</li>
    <li>The chapter titles should loosely follow the order the author chose (chronological if they selected life stages, or thematic if they selected lessons/challenges/turning points).</li>
    <li>The chapter titles should reflect the author's stated main reason for sharing their story (legacy, inspiration, education, or transformation).</li>
    <li>Use vivid, compelling, and memorable language that captures emotion, growth, or pivotal moments.</li>
    <li>Avoid using Markdown or HTML formatting in your response.</li>
  </ol>
  <br />
  The provided information is as follows:
  <ul>
    <li>Book title: ${props.title}</li>
    <li>Book subtitle: ${props.subtitle}</li>
    <li>Author's main reason for sharing their story: ${
      questions.value.mainReason
    }</li>
  <li>Main Reason for Sharing Story: ${questions?.value?.mainReason}</li>
  <li>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</li>
  <li>Story Structure Preference: ${questions?.value?.bookOrder}</li>
  <li>Spiritual Background: ${questions?.value?.bookFocused}</li>
  <li>Target Audience Description: ${questions?.value?.description}</li>
  <li>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</li>
  <li>Reader Takeaway: ${questions?.value?.readersTakeaway}</li>
  <li>Book Goals: ${questions?.value?.authorImpact}</li>
    <li>Target audience: ${questions.value.description}</li>
    <li>Target audience age: ${questions.value.age}</li>
  </ul>`;
});

const results = ref('');

onMounted(async () => {
  if (chapters.value) {
    try {
      await generateChapters(true);
    } catch (e) {
      console.error(e);
    }
  }
});
async function generateChapters(invoking: boolean = false) {
  const { title, subtitle } = props;
  if ((!title || !subtitle) && !invoking) {
    warn($q, {
      title: 'Missing information',
      message: 'You must first fill the title, and subtitle',
    });
    return;
  }
  let promptRequest: string = prompt.value;
  if (chapters.value.length && !invoking) {
    const shouldDelete = await confirmOverrideText($q, {
      message:
        'This action will delete all chapters and their content that you have written. Are you sure you want to proceed?',
    });

    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (!invoking) loadingChapters.value = true;

  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value}`;
  }

  try {
    const response = await composeText(promptRequest);

    const newChapters: NewChapterOutline[] = response
      .trim()
      .split('\n')
      .filter((line) => /^\d+\./.test(line))
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0)
      .map((chapter, idx) => ({
        title: chapter,
        wordCount: 0,
        isSection: false,
        number: idx + 1,
        outline: '',
      }));
    if (!invoking) chapters.value = newChapters;

    // save the results
    results.value = `${results.value} ${newChapters}`;
  } catch (e) {
    console.error(e);
    showError($q, (e as Error).message);
  } finally {
    loadingChapters.value = false;
  }
}
</script>

<!-- Styles are now in global onboarding-common.scss -->
