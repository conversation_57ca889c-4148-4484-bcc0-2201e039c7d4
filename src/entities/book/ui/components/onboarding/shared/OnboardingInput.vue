<template>
  <q-input
    v-model="inputValue"
    :outlined="outlined"
    :label="label"
    :placeholder="placeholder"
    :disable="disabled"
    :loading="loading"
    :type="type"
    :rows="rows"
    :size="size"
    class="onboarding-fade-in"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:prepend v-if="prependIcon">
      <q-icon :name="prependIcon" :color="iconColor" />
    </template>
    <template v-slot:append v-if="appendIcon">
      <q-icon :name="appendIcon" :color="iconColor" />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue: string;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  outlined?: boolean;
  type?: string;
  rows?: number;
  size?: string;
  prependIcon?: string;
  appendIcon?: string;
  iconColor?: string;
}

const props = withDefaults(defineProps<Props>(), {
  outlined: true,
  type: 'text',
  iconColor: 'primary',
});

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
