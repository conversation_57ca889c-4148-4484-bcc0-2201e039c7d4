# Onboarding Shared Components

This directory contains reusable components for the onboarding flow, designed to maintain consistency across different book types (nonfiction, autobiography, faith story) while following the established design patterns from StreamingDialog.vue.

## Components Overview

### 🎯 Core Components

#### `OnboardingStepWrapper`
A comprehensive wrapper component that combines all other shared components into a complete step layout.

```vue
<OnboardingStepWrapper
  theme="faith-story"
  :steps="steps"
  :current-step="currentStep"
  content-title="Create Your Title"
  content-subtitle="Craft a compelling title for your book"
  content-icon="title"
  :show-progress="true"
  :total-steps="3"
  :can-proceed="isValid"
  @next="handleNext"
  @complete="handleComplete"
>
  <!-- Your form content here -->
  <OnboardingFormSection
    label="Book Details"
    description="Enter your book information"
    icon="book"
  >
    <OnboardingInput
      v-model="title"
      label="Book Title"
      placeholder="Enter your book title..."
      prepend-icon="title"
    />
  </OnboardingFormSection>
</OnboardingStepWrapper>
```

#### `OnboardingHeader`
Header component with title, subtitle, icon, and optional actions.

```vue
<OnboardingHeader
  title="Book Creation Wizard"
  subtitle="Let's create your amazing book together"
  icon="auto_stories"
  :show-actions="true"
  :auto-save="true"
  @save="handleSave"
/>
```

#### `OnboardingStepIndicator`
Visual step progress indicator with icons and labels.

```vue
<OnboardingStepIndicator
  :steps="[
    { id: 'mission', label: 'Mission' },
    { id: 'title', label: 'Title & Subtitle' },
    { id: 'chapters', label: 'Chapters' }
  ]"
  :current-step="1"
/>
```

### 📝 Form Components

#### `OnboardingFormSection`
Wrapper for form sections with label, description, and icon.

```vue
<OnboardingFormSection
  label="Book Information"
  description="Provide basic details about your book"
  icon="info"
>
  <!-- Form fields go here -->
</OnboardingFormSection>
```

#### `OnboardingInput`
Enhanced input component with consistent styling and icons.

```vue
<OnboardingInput
  v-model="bookTitle"
  label="Book Title"
  placeholder="Enter your book title..."
  prepend-icon="title"
  icon-color="primary"
  :disabled="loading"
/>
```

#### `OnboardingOptionGrid`
Grid layout for selectable options with icons and descriptions.

```vue
<OnboardingOptionGrid
  :options="[
    {
      label: 'Self-Help',
      value: 'self-help',
      description: 'Personal development and improvement',
      icon: 'psychology'
    },
    {
      label: 'Business',
      value: 'business',
      description: 'Professional and entrepreneurial topics',
      icon: 'business'
    }
  ]"
  v-model:selected-value="selectedGenre"
  :columns="2"
  @select="handleSelection"
/>
```

### 🤖 AI Components

#### `MannyButton`
Consistent Manny AI assistant button with loading states.

```vue
<MannyButton
  label="Generate Ideas"
  variant="generate"
  size="large"
  :loading="generating"
  :disabled="!canGenerate"
  tooltip="Ask Manny for AI-powered suggestions"
  @click="generateSuggestions"
/>
```

#### `OnboardingSuggestionList`
Display AI-generated suggestions in organized columns.

```vue
<OnboardingSuggestionList
  :columns="[
    {
      key: 'titles',
      title: 'Title Suggestions',
      icon: 'title',
      suggestions: titleSuggestions
    },
    {
      key: 'subtitles',
      title: 'Subtitle Suggestions',
      icon: 'subtitles',
      suggestions: subtitleSuggestions
    }
  ]"
  :selected-values="{ titles: selectedTitle, subtitles: selectedSubtitle }"
  @select="handleSuggestionSelect"
/>
```

### 📚 Content Components

#### `OnboardingChapterList`
Draggable chapter list with add/delete functionality.

```vue
<OnboardingChapterList
  v-model="chapters"
  :loading="generatingChapters"
  :allow-reorder="true"
  empty-state-text="Add your first chapter"
  loading-text="Manny is generating chapters..."
  @add-chapter="addNewChapter"
  @delete-chapter="deleteChapter"
/>
```

### 🎨 UI Components

#### `OnboardingActionButtons`
Consistent action buttons for navigation and completion.

```vue
<OnboardingActionButtons
  :show-back-button="currentStep > 0"
  :show-next-button="currentStep < totalSteps - 1"
  :show-complete-button="currentStep === totalSteps - 1"
  :can-proceed="isStepValid"
  :loading="saving"
  @back="goToPreviousStep"
  @next="goToNextStep"
  @complete="completeOnboarding"
>
  <template #center-actions>
    <MannyButton
      label="Get Help"
      variant="round"
      @click="showHelp"
    />
  </template>
</OnboardingActionButtons>
```

#### `OnboardingProgressBar`
Visual progress indicator for multi-step flows.

```vue
<OnboardingProgressBar
  :current-step="currentStep + 1"
  :total-steps="totalSteps"
  :show-text="true"
  custom-text="Creating your book foundation..."
/>
```

#### `OnboardingLoadingState`
Various loading states with consistent styling.

```vue
<OnboardingLoadingState
  variant="spinner"
  text="Generating content..."
/>

<OnboardingLoadingState
  variant="skeleton"
  :skeleton-line-count="5"
/>

<OnboardingLoadingState
  variant="dots"
  text="Manny is thinking..."
/>
```

## Theming

Components support three themes:
- `default` - Standard blue theme
- `faith-story` - Purple theme for faith stories
- `autobiography` - Orange theme for autobiographies

Apply themes using the `theme` prop or CSS classes:

```vue
<OnboardingStepWrapper theme="faith-story" />
<!-- or -->
<div class="faith-story-theme">
  <OnboardingHeader />
</div>
```

## Styling

All components use the shared `onboarding-common.scss` file for consistent styling. The SCSS includes:

- **Responsive design** - Mobile-first approach with breakpoints
- **Accessibility** - Focus states, high contrast support, reduced motion
- **Animations** - Smooth transitions and micro-interactions
- **Modal support** - Optimized for modal dialogs
- **Icon system** - Consistent icon sizing and styling

## Best Practices

1. **Use OnboardingStepWrapper** for complete step layouts
2. **Combine components** rather than building from scratch
3. **Follow theme conventions** for consistent branding
4. **Include proper icons** for better visual hierarchy
5. **Handle loading states** for better UX
6. **Use proper accessibility** attributes and focus management
7. **Test on mobile** devices for responsive behavior

## Migration Guide

To migrate existing onboarding components:

1. Replace custom headers with `OnboardingHeader`
2. Use `OnboardingFormSection` for form organization
3. Replace input fields with `OnboardingInput`
4. Use `MannyButton` for all AI interactions
5. Implement `OnboardingActionButtons` for navigation
6. Add proper theming classes

This ensures consistency across all onboarding flows while maintaining the established design patterns.
