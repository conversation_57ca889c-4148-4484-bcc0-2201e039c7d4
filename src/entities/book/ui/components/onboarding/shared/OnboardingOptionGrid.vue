<template>
  <div class="onboarding-options">
    <div class="option-group" :class="gridClass">
      <div
        v-for="(option, index) in options"
        :key="option.value || index"
        class="option-item"
        :class="{ selected: isSelected(option.value) }"
        @click="selectOption(option.value)"
      >
        <div class="option-icon" v-if="option.icon">
          <q-icon :name="option.icon" />
        </div>
        <div class="option-label">{{ option.label }}</div>
        <div class="option-description" v-if="option.description">
          {{ option.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Option {
  label: string;
  value: string;
  description?: string;
  icon?: string;
}

interface Props {
  options: Option[];
  selectedValue?: string;
  selectedValues?: string[];
  multiple?: boolean;
  columns?: 2 | 3;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  columns: 2,
});

const emit = defineEmits<{
  select: [value: string];
  'update:selectedValue': [value: string];
  'update:selectedValues': [values: string[]];
}>();

const gridClass = computed(() => {
  return props.columns === 3 ? 'grid-3' : 'grid-2';
});

const isSelected = (value: string) => {
  if (props.multiple) {
    return props.selectedValues?.includes(value) || false;
  }
  return props.selectedValue === value;
};

const selectOption = (value: string) => {
  if (props.multiple) {
    const currentValues = props.selectedValues || [];
    let newValues: string[];

    if (currentValues.includes(value)) {
      newValues = currentValues.filter((v) => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    emit('update:selectedValues', newValues);
  } else {
    emit('update:selectedValue', value);
  }

  emit('select', value);
};
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
