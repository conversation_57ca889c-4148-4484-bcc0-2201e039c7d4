<template>
  <div class="onboarding-actions">
    <div class="action-group" v-if="showLeftActions">
      <slot name="left-actions">
        <q-btn
          v-if="showBackButton"
          class="action-btn btn-secondary"
          :label="backLabel"
          icon="arrow_back"
          @click="$emit('back')"
          :disable="disabled"
        />
      </slot>
    </div>

    <div class="action-group">
      <slot name="center-actions" />
    </div>

    <div class="action-group">
      <slot name="right-actions">
        <q-btn
          v-if="showSkipButton"
          class="action-btn btn-secondary"
          :label="skipLabel"
          @click="$emit('skip')"
          :disable="disabled"
          flat
        />
        <q-btn
          v-if="showNextButton"
          class="action-btn btn-primary"
          :label="nextLabel"
          :icon="nextIcon"
          @click="$emit('next')"
          :disable="disabled || !canProceed"
          :loading="loading"
        />
        <q-btn
          v-if="showCompleteButton"
          class="action-btn btn-primary"
          :label="completeLabel"
          :icon="completeIcon"
          @click="$emit('complete')"
          :disable="disabled || !canProceed"
          :loading="loading"
        />
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  showBackButton?: boolean;
  showSkipButton?: boolean;
  showNextButton?: boolean;
  showCompleteButton?: boolean;
  showLeftActions?: boolean;
  backLabel?: string;
  skipLabel?: string;
  nextLabel?: string;
  completeLabel?: string;
  nextIcon?: string;
  completeIcon?: string;
  disabled?: boolean;
  loading?: boolean;
  canProceed?: boolean;
}

withDefaults(defineProps<Props>(), {
  showBackButton: false,
  showSkipButton: false,
  showNextButton: true,
  showCompleteButton: false,
  showLeftActions: true,
  backLabel: 'Back',
  skipLabel: 'Skip',
  nextLabel: 'Next',
  completeLabel: 'Complete',
  nextIcon: 'arrow_forward',
  completeIcon: 'check',
  disabled: false,
  loading: false,
  canProceed: true,
});

defineEmits<{
  back: [];
  skip: [];
  next: [];
  complete: [];
}>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
