<template>
  <div class="suggestions-section" v-if="suggestions.length > 0">
    <div class="suggestions-grid" :class="gridClass">
      <div
        v-for="column in columns"
        :key="column.key"
        class="suggestion-column"
      >
        <div class="column-header">
          <div class="column-icon" v-if="column.icon">
            <q-icon :name="column.icon" />
          </div>
          {{ column.title }}
        </div>
        <div class="suggestion-list">
          <div
            v-for="(suggestion, index) in column.suggestions"
            :key="`${column.key}-${index}`"
            class="suggestion-item"
            :class="{ selected: isSelected(column.key, suggestion.value) }"
            @click="selectSuggestion(column.key, suggestion.value)"
          >
            <div class="suggestion-content">
              <div class="suggestion-text">{{ suggestion.label }}</div>
              <q-icon :name="checkIcon" class="check-icon" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Suggestion {
  label: string;
  value: string;
}

interface Column {
  key: string;
  title: string;
  icon?: string;
  suggestions: Suggestion[];
}

interface Props {
  suggestions: Suggestion[];
  columns?: Column[];
  selectedValues?: Record<string, string>;
  checkIcon?: string;
  gridColumns?: number;
}

const props = withDefaults(defineProps<Props>(), {
  checkIcon: 'check',
  gridColumns: 2,
});

const emit = defineEmits<{
  select: [key: string, value: string];
}>();

const gridClass = computed(() => {
  return props.gridColumns === 3 ? 'grid-3' : 'grid-2';
});

const isSelected = (key: string, value: string) => {
  return props.selectedValues?.[key] === value;
};

const selectSuggestion = (key: string, value: string) => {
  emit('select', key, value);
};
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
