<template>
  <div class="onboarding-steps">
    <div class="steps-container">
      <template v-for="(step, index) in steps" :key="step.id">
        <div class="step-item">
          <div
            class="step-number"
            :class="{
              active: index === currentStep,
              completed: index < currentStep,
              inactive: index > currentStep,
            }"
          >
            <q-icon
              v-if="index < currentStep"
              name="check"
              size="14px"
            />
            <span v-else>{{ index + 1 }}</span>
          </div>
          <div
            class="step-label"
            :class="{
              active: index === currentStep,
              completed: index < currentStep,
            }"
          >
            {{ step.label }}
          </div>
        </div>
        <div
          v-if="index < steps.length - 1"
          class="step-connector"
          :class="{ completed: index < currentStep }"
        ></div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Step {
  id: string;
  label: string;
}

interface Props {
  steps: Step[];
  currentStep: number;
}

defineProps<Props>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
