<template>
  <div class="onboarding-chapters">
    <div class="chapter-list" v-if="chapters.length">
      <draggable
        v-model="chapterList"
        item-key="number"
        class="chapter-draggable"
        @update:model-value="$emit('update:modelValue', $event)"
      >
        <template #item="{ element: chapter, index }">
          <div class="chapter-item onboarding-fade-in">
            <div class="chapter-header">
              <div class="drag-handle" v-if="allowReorder">
                <q-icon name="drag_indicator" />
              </div>
              <div class="chapter-number">{{ index + 1 }}</div>
              <div class="chapter-content">
                <OnboardingInput
                  v-model="chapter.title"
                  :loading="loading"
                  :disabled="loading"
                  :label="`Chapter ${index + 1}`"
                  :placeholder="`Enter chapter ${index + 1} title...`"
                  class="chapter-input"
                />
              </div>
              <div class="chapter-actions">
                <q-btn
                  icon="delete"
                  class="action-btn delete-btn"
                  flat
                  round
                  :disable="loading"
                  @click="deleteChapter(index)"
                >
                  <q-tooltip>Delete Chapter</q-tooltip>
                </q-btn>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <div
      class="add-chapter-btn"
      @click="$emit('add-chapter')"
      v-if="!chapters.length"
    >
      <q-icon name="add" class="add-icon" />
      <span>{{ emptyStateText }}</span>
    </div>

    <div class="onboarding-loading" v-if="loading">
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import draggable from 'vuedraggable';
import OnboardingInput from './OnboardingInput.vue';

interface Chapter {
  title: string;
  number: number;
  [key: string]: any;
}

interface Props {
  modelValue: Chapter[];
  loading?: boolean;
  allowReorder?: boolean;
  emptyStateText?: string;
  loadingText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  allowReorder: true,
  emptyStateText: 'Add your first chapter',
  loadingText: 'Manny is generating chapter suggestions...',
});

const emit = defineEmits<{
  'update:modelValue': [chapters: Chapter[]];
  'add-chapter': [];
  'delete-chapter': [index: number];
}>();

const chapterList = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const deleteChapter = (index: number) => {
  emit('delete-chapter', index);
};
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
