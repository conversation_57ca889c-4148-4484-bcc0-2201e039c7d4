<template>
  <div class="onboarding-loading">
    <div v-if="variant === 'spinner'" class="loading-spinner">
      <div class="spinner"></div>
    </div>
    
    <div v-else-if="variant === 'skeleton'" class="loading-skeleton">
      <div
        v-for="line in skeletonLines"
        :key="line.id"
        class="skeleton-line"
        :class="line.width"
      ></div>
    </div>
    
    <div v-else-if="variant === 'dots'" class="loading-dots">
      <div class="typing-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    
    <div class="loading-text" v-if="text">
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  variant?: 'spinner' | 'skeleton' | 'dots';
  text?: string;
  skeletonLineCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'spinner',
  skeletonLineCount: 3,
});

const skeletonLines = computed(() => {
  const lines = [];
  const widths = ['short', 'medium', 'long'];
  
  for (let i = 0; i < props.skeletonLineCount; i++) {
    lines.push({
      id: i,
      width: widths[i % widths.length],
    });
  }
  
  return lines;
});
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss

.typing-dots {
  display: inline-flex;
  gap: 4px;
  
  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: -0.32s;
    }
    
    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
