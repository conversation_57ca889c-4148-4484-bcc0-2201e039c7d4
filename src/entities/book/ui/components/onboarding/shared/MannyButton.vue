<template>
  <q-btn
    class="manny-button"
    :class="{
      'generate-btn': variant === 'generate',
      round: variant === 'round',
    }"
    :loading="loading"
    :disable="disabled"
    @click="$emit('click')"
  >
    <div
      class="manny-avatar"
      :class="{
        loading: loading,
        large: size === 'large',
      }"
    >
      <q-img src="robot.png" :width="avatarSize" />
    </div>
    <span v-if="variant !== 'round'">{{ label }}</span>
    <q-tooltip v-if="tooltip">{{ tooltip }}</q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  label?: string;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'default' | 'generate' | 'round';
  size?: 'default' | 'large';
  tooltip?: string;
}

const props = withDefaults(defineProps<Props>(), {
  label: 'Ask Manny',
  loading: false,
  disabled: false,
  variant: 'default',
  size: 'default',
});

const avatarSize = computed(() => {
  if (props.variant === 'round') return '24px';
  if (props.size === 'large') return '24px';
  return '20px';
});

defineEmits<{
  click: [];
}>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
