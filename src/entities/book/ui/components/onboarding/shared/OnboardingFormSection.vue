<template>
  <div class="form-section">
    <div class="section-label" v-if="label">
      <div class="label-icon" v-if="icon">
        <q-icon :name="icon" />
      </div>
      {{ label }}
    </div>
    <div class="section-description" v-if="description">
      {{ description }}
    </div>
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  label?: string;
  description?: string;
  icon?: string;
}

defineProps<Props>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
