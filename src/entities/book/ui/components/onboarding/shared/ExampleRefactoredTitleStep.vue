<template>
  <OnboardingStepWrapper
    theme="default"
    content-title="Create Title & Subtitle"
    content-subtitle="Craft a powerful title and subtitle combination that captures your book's essence and attracts your target readers."
    content-icon="title"
    :show-actions="true"
    :show-next-button="true"
    :can-proceed="canProceed"
    @next="$emit('next')"
  >
    <!-- Title & Subtitle Input Section -->
    <OnboardingFormSection
      label="Book Title & Subtitle"
      description="Create a compelling title and subtitle that work together to communicate your book's value proposition."
      icon="edit"
    >
      <div class="form-grid grid-2">
        <OnboardingInput
          v-model="title"
          label="Book Title"
          placeholder="Enter your book title..."
          prepend-icon="title"
          :disabled="loading"
        />

        <OnboardingInput
          v-model="subtitle"
          label="Book Subtitle"
          placeholder="Enter your book subtitle..."
          prepend-icon="subtitles"
          :disabled="loading"
        />
      </div>

      <div class="form-actions">
        <MannyButton
          label="Generate Title & Subtitle Ideas"
          variant="generate"
          :loading="loading"
          :disabled="!canGenerate"
          tooltip="Ask Manny for AI-powered suggestions"
          @click="generateTitleSubtitlePairs"
        />
      </div>
    </OnboardingFormSection>

    <!-- AI Suggestions Section -->
    <OnboardingFormSection
      v-if="suggestions.titles.length > 0"
      label="AI Suggestions"
      description="Choose from these AI-generated combinations or mix and match titles and subtitles."
      icon="auto_awesome"
    >
      <OnboardingSuggestionList
        :columns="[
          {
            key: 'titles',
            title: 'Title Suggestions',
            icon: 'title',
            suggestions: titleOptions
          },
          {
            key: 'subtitles',
            title: 'Subtitle Suggestions',
            icon: 'subtitles',
            suggestions: subtitleOptions
          }
        ]"
        :selected-values="{ titles: title, subtitles: subtitle }"
        @select="handleSuggestionSelect"
      />
    </OnboardingFormSection>

    <!-- Loading State -->
    <OnboardingLoadingState
      v-if="loading"
      variant="spinner"
      text="Manny is generating title and subtitle ideas..."
    />
  </OnboardingStepWrapper>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import {
  OnboardingStepWrapper,
  OnboardingFormSection,
  OnboardingInput,
  OnboardingSuggestionList,
  OnboardingLoadingState,
  MannyButton,
  type OnboardingSuggestion,
} from './index';

const props = withDefaults(
  defineProps<{
    titleModelValue: string;
    subtitleModelValue: string;
    mission: string;
    transformations?: string;
    coreNeeds?: string;
    updating?: boolean;
  }>(),
  {
    updating: false,
  },
);

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
  next: [];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);

const $q = useQuasar();

// Component state
const loading = ref(false);
const suggestions = ref({
  titles: [] as string[],
  subtitles: [] as string[],
});
const results = ref('');

// Computed properties
const canGenerate = computed(() => {
  return props.mission ? true : props.coreNeeds && props.transformations;
});

const canProceed = computed(() => {
  return title.value.trim() && subtitle.value.trim();
});

const titleOptions = computed((): OnboardingSuggestion[] => {
  return suggestions.value.titles.map((title) => ({
    label: title,
    value: title,
  }));
});

const subtitleOptions = computed((): OnboardingSuggestion[] => {
  return suggestions.value.subtitles.map((subtitle) => ({
    label: subtitle,
    value: subtitle,
  }));
});

// Event handlers
const handleSuggestionSelect = (key: string, value: string) => {
  if (key === 'titles') {
    title.value = value;
  } else if (key === 'subtitles') {
    subtitle.value = value;
  }
};

// AI generation logic (simplified for example)
const generateTitleSubtitlePairs = async () => {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message: 'Manny needs your mission statement to generate suggestions.',
    });
    return;
  }

  if ((title.value || subtitle.value) && suggestions.value.titles.length === 0) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) return;
  }

  loading.value = true;

  try {
    // Simplified prompt for example
    const prompt = `Generate 5 title and subtitle pairs for a book with mission: "${props.mission}"`;
    const response = await composeText(prompt);
    
    // Parse response (simplified)
    const pairs = parseTitleSubtitleResponse(response);
    suggestions.value = pairs;
  } catch (error) {
    console.error(error);
    $q.notify({
      type: 'negative',
      message: 'Failed to generate suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
};

const parseTitleSubtitleResponse = (response: string) => {
  // Simplified parsing logic
  const lines = response.trim().split('\n').filter(line => line.trim());
  const titles: string[] = [];
  const subtitles: string[] = [];
  
  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      titles.push(lines[i].trim());
      subtitles.push(lines[i + 1].trim());
    }
  }
  
  return { titles, subtitles };
};
</script>

<style scoped lang="scss">
// All styles are handled by onboarding-common.scss
// This component demonstrates the clean separation of concerns
</style>
