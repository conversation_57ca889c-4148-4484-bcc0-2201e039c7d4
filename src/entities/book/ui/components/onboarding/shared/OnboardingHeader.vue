<template>
  <div class="onboarding-header">
    <div class="header-content">
      <div class="header-icon" v-if="icon">
        <q-icon :name="icon" />
      </div>
      <div class="header-text">
        <div class="title">{{ title }}</div>
        <div class="subtitle" v-if="subtitle">{{ subtitle }}</div>
      </div>
      <div class="header-actions" v-if="showActions">
        <div class="auto-save-indicator" v-if="autoSave">
          <q-icon name="cloud_done" size="14px" />
          <span>Auto-saved</span>
        </div>
        <q-btn
          v-if="showSaveButton"
          class="save-btn"
          flat
          icon="save"
          label="Save"
          @click="$emit('save')"
          :loading="saving"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  subtitle?: string;
  icon?: string;
  showActions?: boolean;
  autoSave?: boolean;
  showSaveButton?: boolean;
  saving?: boolean;
}

withDefaults(defineProps<Props>(), {
  showActions: false,
  autoSave: false,
  showSaveButton: false,
  saving: false,
});

defineEmits<{
  save: [];
}>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
