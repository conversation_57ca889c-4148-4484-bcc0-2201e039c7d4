<template>
  <div class="onboarding-progress">
    <div class="progress-bar">
      <div
        class="progress-fill"
        :style="{ width: `${progressPercentage}%` }"
      ></div>
    </div>
    <div class="progress-text" v-if="showText">
      {{ progressText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  currentStep: number;
  totalSteps: number;
  showText?: boolean;
  customText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  showText: true,
});

const progressPercentage = computed(() => {
  return Math.round((props.currentStep / props.totalSteps) * 100);
});

const progressText = computed(() => {
  if (props.customText) {
    return props.customText;
  }
  return `Step ${props.currentStep} of ${props.totalSteps} (${progressPercentage.value}%)`;
});
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
