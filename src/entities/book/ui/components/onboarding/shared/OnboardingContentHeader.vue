<template>
  <div class="content-header">
    <div class="content-title">
      <div class="title-icon" v-if="icon">
        <q-icon :name="icon" />
      </div>
      {{ title }}
    </div>
    <div class="content-subtitle" v-if="subtitle">
      {{ subtitle }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  subtitle?: string;
  icon?: string;
}

defineProps<Props>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
