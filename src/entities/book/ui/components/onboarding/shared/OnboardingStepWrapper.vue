<template>
  <div class="onboarding-step" :class="themeClass">
    <div class="step-container">
      <!-- Header Section -->
      <OnboardingHeader
        v-if="showHeader"
        :title="headerTitle"
        :subtitle="headerSubtitle"
        :icon="headerIcon"
        :show-actions="showHeaderActions"
        :auto-save="autoSave"
        :show-save-button="showSaveButton"
        :saving="saving"
        @save="$emit('save')"
      />

      <!-- Step Indicator -->
      <OnboardingStepIndicator
        v-if="showStepIndicator && steps.length > 0"
        :steps="steps"
        :current-step="currentStep"
      />

      <!-- Main Content -->
      <div class="onboarding-content">
        <!-- Content Header -->
        <OnboardingContentHeader
          v-if="contentTitle"
          :title="contentTitle"
          :subtitle="contentSubtitle"
          :icon="contentIcon"
        />

        <!-- Content Body -->
        <div class="content-body">
          <div class="onboarding-form">
            <slot />
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <OnboardingProgressBar
        v-if="showProgress"
        :current-step="currentStep + 1"
        :total-steps="totalSteps"
        :show-text="showProgressText"
        :custom-text="progressText"
      />

      <!-- Action Buttons -->
      <OnboardingActionButtons
        v-if="showActions"
        :show-back-button="showBackButton"
        :show-skip-button="showSkipButton"
        :show-next-button="showNextButton"
        :show-complete-button="showCompleteButton"
        :back-label="backLabel"
        :skip-label="skipLabel"
        :next-label="nextLabel"
        :complete-label="completeLabel"
        :next-icon="nextIcon"
        :complete-icon="completeIcon"
        :disabled="actionsDisabled"
        :loading="actionsLoading"
        :can-proceed="canProceed"
        @back="$emit('back')"
        @skip="$emit('skip')"
        @next="$emit('next')"
        @complete="$emit('complete')"
      >
        <template #left-actions>
          <slot name="left-actions" />
        </template>
        <template #center-actions>
          <slot name="center-actions" />
        </template>
        <template #right-actions>
          <slot name="right-actions" />
        </template>
      </OnboardingActionButtons>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  OnboardingHeader,
  OnboardingStepIndicator,
  OnboardingContentHeader,
  OnboardingProgressBar,
  OnboardingActionButtons,
  type OnboardingStep,
} from './index';

interface Props {
  // Theme
  theme?: 'default' | 'faith-story' | 'autobiography';

  // Header
  showHeader?: boolean;
  headerTitle?: string;
  headerSubtitle?: string;
  headerIcon?: string;
  showHeaderActions?: boolean;
  autoSave?: boolean;
  showSaveButton?: boolean;
  saving?: boolean;

  // Step Indicator
  showStepIndicator?: boolean;
  steps?: OnboardingStep[];
  currentStep?: number;

  // Content
  contentTitle?: string;
  contentSubtitle?: string;
  contentIcon?: string;

  // Progress
  showProgress?: boolean;
  totalSteps?: number;
  showProgressText?: boolean;
  progressText?: string;

  // Actions
  showActions?: boolean;
  showBackButton?: boolean;
  showSkipButton?: boolean;
  showNextButton?: boolean;
  showCompleteButton?: boolean;
  backLabel?: string;
  skipLabel?: string;
  nextLabel?: string;
  completeLabel?: string;
  nextIcon?: string;
  completeIcon?: string;
  actionsDisabled?: boolean;
  actionsLoading?: boolean;
  canProceed?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'default',
  showHeader: false,
  showHeaderActions: false,
  autoSave: false,
  showSaveButton: false,
  saving: false,
  showStepIndicator: true,
  steps: () => [],
  currentStep: 0,
  showProgress: false,
  totalSteps: 1,
  showProgressText: true,
  showActions: true,
  showBackButton: false,
  showSkipButton: false,
  showNextButton: true,
  showCompleteButton: false,
  actionsDisabled: false,
  actionsLoading: false,
  canProceed: true,
});

const themeClass = computed(() => {
  switch (props.theme) {
    case 'faith-story':
      return 'faith-story-theme';
    case 'autobiography':
      return 'autobiography-theme';
    default:
      return '';
  }
});

defineEmits<{
  save: [];
  back: [];
  skip: [];
  next: [];
  complete: [];
}>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
