<template>
  <q-card-section class="flex justify-between">
    <div class="text-h7">
      {{ updating ? 'Edit' : 'Write' }} your Non-Fiction Book
    </div>
    <q-btn
      v-if="mode === Modes.Manual"
      @click="mode = Modes.Help"
      color="primary"
      outline
    >
      Ask <PERSON>!
      <q-tooltip>Ask <PERSON></q-tooltip>
    </q-btn>
    <q-btn v-else @click="mode = Modes.Manual" color="danger" outline>
      Skip <PERSON>!
      <q-tooltip>Skip <PERSON></q-tooltip>
    </q-btn>
  </q-card-section>
  <q-separator />

  <q-scroll-area
    :thumb-style="thumbStyle"
    :bar-style="barStyle"
    :style="{
      height: `calc(100vh - ${isScreenBiggerMd ? '390px' : '250px'})`,
    }"
  >
    <q-card-section>
      <q-form
        v-if="mode === Modes.Help"
        @submit.prevent="generateMission(false)"
      >
        <section>
          <q-select
            label="Is your target audience a specific gender?"
            v-model="gender"
            :options="['Male', 'Female', 'Both']"
            required
            outlined
          />
        </section>
        <section class="q-mt-md">
          <q-select
            label="Is your target audience a specific age?"
            v-model="age"
            :options="ageOptions"
            multiple
            use-chips
            required
            outlined
          />
        </section>
        <section class="q-mt-md">
          <q-input
            label="Describe your target audience in a few words."
            v-model="description"
            autogrow
            required
            outlined
          >
            <template v-slot:append>
              <q-btn
                flat
                icon="mic"
                @click="
                  selectedField = 'description';
                  transcriptionDialog = true;
                "
              >
                <q-tooltip>Transcribe</q-tooltip>
              </q-btn>
            </template>
          </q-input>
        </section>
        <section class="q-mt-md">
          <q-input
            label="What is the core need that your book will meet for them?"
            v-model="need"
            autogrow
            required
            outlined
          >
            <template v-slot:append>
              <q-btn
                flat
                icon="img:robot.png"
                :loading="loadingNeed"
                :disable="loadingNeed"
                @click="generateNeed(false)"
              >
                <q-tooltip>Ask Manny</q-tooltip>
              </q-btn>
            </template>
          </q-input>
        </section>
        <section class="q-mt-md">
          <q-input
            label="List 3-5 problems and fears that your ideal reader has."
            v-model="problems"
            autogrow
            required
            outlined
          >
            <template v-slot:append>
              <q-btn
                flat
                icon="img:robot.png"
                :loading="loadingProblems"
                :disable="loadingProblems"
                @click="generateProblems(false)"
              >
                <q-tooltip>Ask Manny</q-tooltip>
              </q-btn>
            </template>
          </q-input>
        </section>
        <section class="q-mt-md">
          <q-input
            label="When someone completes your book, what are 3-4 transformations that they will
      experience?"
            v-model="readerImpact"
            autogrow
            required
            outlined
          >
            <template v-slot:append>
              <q-btn
                flat
                icon="img:robot.png"
                :loading="loadingTransformations"
                :disable="loadingTransformations"
                @click="generateTransformations(false)"
              >
                <q-tooltip>Ask Manny</q-tooltip>
              </q-btn>
            </template>
          </q-input>
        </section>
        <section class="q-mt-md">
          <q-input
            label="What are 2-3 things that you want this book to do for you and your business?"
            v-model="authorImpact"
            required
            outlined
          >
            <template v-slot:append>
              <q-btn
                flat
                icon="mic"
                @click="
                  selectedField = 'authorImpact';
                  transcriptionDialog = true;
                "
              >
                <q-tooltip>Transcribe</q-tooltip>
              </q-btn>
            </template>
          </q-input>
        </section>
        <section class="q-my-lg flex justify-end">
          <q-btn
            type="submit"
            :loading="loadingMission"
            color="primary"
            outline
          >
            Let Manny Write!
            <q-icon name="img:robot.png" class="q-ml-xs" />
            <q-tooltip>Let Manny Write!</q-tooltip>
          </q-btn>
        </section>
      </q-form>
      <q-editor
        class="q-mt-sm mission-statement"
        v-model="mission"
        placeholder="Example: My target audience is Bob, a 37 year old father from Canada, who is a project manager. He is concerned about steady work over a long period of time, developing his skills, and advancing in his career. This book will give him a path about never having to worry where his next client is going to come from, increases his income by double, and give him free time. It will help you by having Bob join your program, and invest with you for ongoing coaching."
      />
    </q-card-section>
  </q-scroll-area>

  <q-dialog v-model="transcriptionDialog">
    <BookTranscriptionApp
      persistent
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useVModels, toRefs } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { barStyle, thumbStyle } from 'src/entities/setting';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';

export interface Questions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  description: string;
  need: string;
  problems: string;
  readerImpact: string;
  authorImpact: string;
}

const props = withDefaults(
  defineProps<{
    modelValue: string;
    questions: Questions;
    updating?: boolean;
    isScreenBiggerMd?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'update:questions': [value: Questions];
}>();
const { modelValue: mission, questions } = useVModels(props, emit);
const { gender, age, description, need, problems, authorImpact, readerImpact } =
  toRefs(questions);

const $q = useQuasar();

const selectedField = ref('');
const transcriptionDialog = ref(false);
const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'description':
      description.value = content;
      break;
    case 'authorImpact':
      authorImpact.value = content;
      break;
  }
  transcriptionDialog.value = false;
};

const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

enum Modes {
  Manual,
  Help,
}

const mode = ref(Modes.Manual);

const ageOptions = ['Under 18', '18-25', '25-35', '35-50', '50-65', '65+'];

if (props.modelValue) {
  mode.value = Modes.Manual;
}

const loadingNeed = ref(false);
async function generateNeed(invoking: boolean = false) {
  if (
    (!gender.value || age.value.length === 0 || !description.value) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.need;
  if (need.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.need =
      typeof results.value.need === 'string'
        ? results.value.need
        : await results.value.need;
  }

  if (!invoking) loadingNeed.value = true;

  if (results.value.need) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.need}`;
  }
  try {
    const response = await composeText(promptRequest);
    if (!invoking) need.value = response;
    // save the results
    results.value.need = `${results.value.need} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingNeed.value = false;
  }
}

const loadingProblems = ref(false);
async function generateProblems(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !need.value) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.problems;
  if (problems.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.problems =
      typeof results.value.problems === 'string'
        ? results.value.problems
        : await results.value.problems;
  }

  if (!invoking) loadingProblems.value = true;

  try {
    const response = await composeText(promptRequest);
    if (!invoking) problems.value = response;
    // save the results
    results.value.problems = `${results.value.problems} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingProblems.value = false;
  }
}

const loadingTransformations = ref(false);
async function generateTransformations(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !need.value) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.transformations;
  if (readerImpact.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.transformations =
      typeof results.value.transformations === 'string'
        ? results.value.transformations
        : await results.value.transformations;
  }

  if (!invoking) loadingTransformations.value = true;

  try {
    const response = await composeText(promptRequest);
    readerImpact.value = response;
    // save the results
    if (!invoking)
      results.value.transformations = `${results.value.transformations} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingTransformations.value = false;
  }
}

const loadingMission = ref(false);
async function generateMission(invoking: boolean = false) {
  let promptRequest: string = updatedPrompt.value.mission;
  if (mission.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.mission =
      typeof results.value.mission === 'string'
        ? results.value.mission
        : await results.value.mission;
  }
  if (!invoking) loadingMission.value = true;

  try {
    const response = await composeText(promptRequest);
    if (!invoking) mission.value = response;
    // save the results
    results.value.mission = `${results.value.mission} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingMission.value = false;
  }
}

const ageGroups = computed(() => ageConcat.format(age.value));

// Updating of Open AI Prompt
const results = ref({} as { [key: string]: any });

const promptNeed = computed(
  () => `
  Author's Target Audience: ${description.value}
  Gender: ${gender.value}
  Age: ${ageGroups.value}
  As an author, your goal is to create a list of 3-5 core needs of the target audience provided, using their demographic details.
  Given the details above, provide 3-5 core needs of someone in this gender, age band, and main interest or category. The response should follow the criteria below:
  1. core needs of this avatar using the demographic details
  2. do not separate by age band
  3. less than 10 words
  4. provide each response in a new line, without a number, bullet point, or break tag`,
);

const promptProblems = computed(
  () => `
  Author's Target Audience: ${description.value}
  Gender: ${gender.value}
  Age: ${ageGroups.value}
  As an author, your goal is to create a list of 3-5 problems and fears of the target audience provided, using their demographic details.
  The response should follow the criteria below:
  1. short sentences. less than 10 words
  2. do not separate by age band
  3. problems and fears of this target audiences
  4. provide each response in a new line, without a number, bullet point, or break tag`,
);

const promptTransformations = computed(
  () => `
  Gender: ${gender.value}
  Age: ${ageGroups.value}
  Author's Target Audience: ${description.value}
  Core Needs: ${need.value}
  As an author, your goal is to create a list of 3-4 transformations someone will experience on completing your book, given the avatar demographics, core needs and problems and fears given above. The response should follow the criteria below:
  1. short sentences, less than 10 words
  2. exciting, inspiring, motivating
  3. provide each response in a new line, without a number, bullet point, or break tag`,
);

const promptMission = computed(
  () => `
  Gender: ${gender.value}
  Age groups: ${ageGroups.value}
  Main interest or category: ${description.value}
  Core needs the book will meet: ${need.value}
  Problems and fears: ${problems.value}
  For the author, this book will: ${authorImpact.value}.

  Using the details above, write a mission statement for this avatar given the following criteria:
  1. give the avatar a name using the gender provided
  2. introduce the avatar using name and demographics
  3. write the statement in a punchy, positive, inspiring, sales language
  4. write the statement in no longer than 3-5 sentences and within 60-75 words
  5. always close the mission statement with how the book will help the author of this book

  Sample: "Your target audience is Bob, a 37 year old father from Canada, who is a project manager. He is concerned about steady work over a long period of time, developing his skills, and advancing in his career. This book will give him a path about never having to worry where his next client is going to come from, increases his income by double, and give him free time. It will help you by having Bob join your program, and invest with you for ongoing coaching."`,
);

onMounted(async () => {
  if (age.value.length) {
    mode.value = Modes.Help;
  }
  if (need.value) {
    try {
      await generateNeed(true);
    } catch (e) {
      console.error(e);
    }
  }
  if (problems.value) {
    try {
      await generateProblems(true);
    } catch (e) {
      console.error(e);
    }
  }
  if (readerImpact.value) {
    try {
      await generateTransformations(true);
    } catch (e) {
      console.error(e);
    }
  }
  if (mission.value) {
    try {
      await generateMission(true);
    } catch (e) {
      console.error(e);
    }
  }
});

const updatedPrompt = computed(() => ({
  need: promptNeed.value,
  problems: promptProblems.value,
  transformations: promptTransformations.value,
  mission: promptMission.value,
}));
</script>

<style scoped>
.mission-statement :deep(.q-editor__content)::before {
  opacity: 0.4;
}
</style>
