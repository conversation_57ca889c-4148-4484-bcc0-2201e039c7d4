<template>
  <div class="onboarding-step">
    <div class="step-container">
      <div class="onboarding-content title-subtitle-step">
        <div class="content-header">
          <div class="content-title">
            <div class="title-icon">
              <q-icon name="title" />
            </div>
            {{ updating ? 'Edit Title & Subtitle' : 'Create Title & Subtitle' }}
          </div>
          <div class="content-subtitle">
            Craft a powerful title and subtitle combination that captures your book's essence and attracts your target readers.
          </div>
        </div>

        <div class="content-body">
          <div class="onboarding-form">
            <div class="form-section">
              <div class="section-label">
                <div class="label-icon">
                  <q-icon name="edit" />
                </div>
                Book Title & Subtitle
              </div>
              <div class="section-description">
                Create a compelling title and subtitle that work together to communicate your book's value proposition.
              </div>

              <div class="form-grid grid-2">
                <q-input
                  outlined
                  v-model="title"
                  label="Book Title"
                  class="onboarding-fade-in"
                  :disable="loading"
                  placeholder="Enter your book title..."
                  size="lg"
                >
                  <template v-slot:prepend>
                    <q-icon name="title" color="primary" />
                  </template>
                </q-input>

                <q-input
                  outlined
                  v-model="subtitle"
                  label="Book Subtitle"
                  class="onboarding-fade-in"
                  :disable="loading"
                  placeholder="Enter your book subtitle..."
                  size="lg"
                >
                  <template v-slot:prepend>
                    <q-icon name="subtitles" color="primary" />
                  </template>
                </q-input>
              </div>

              <div class="form-actions">
                <div class="manny-assistant">
                  <q-btn
                    class="manny-button generate-btn"
                    :loading="loading"
                    :disable="loading || !canGenerate"
                    @click="generateTitleSubtitlePairs"
                  >
                    <div class="manny-avatar" :class="{ loading }">
                      <q-img src="robot.png" width="20px" />
                    </div>
                    Generate Title & Subtitle Ideas
                    <q-tooltip>Ask Manny for AI-powered suggestions</q-tooltip>
                  </q-btn>
                </div>
              </div>
            </div>

            <div class="form-section" v-if="suggestions.titles.length > 0">
              <div class="section-label">
                <div class="label-icon">
                  <q-icon name="auto_awesome" />
                </div>
                AI Suggestions
              </div>
              <div class="section-description">
                Choose from these AI-generated combinations or mix and match titles and subtitles.
              </div>

              <div class="suggestions-section">
                <div class="suggestions-grid">
                  <!-- Title Suggestions Column -->
                  <div class="suggestion-column">
                    <div class="column-header">
                      <div class="column-icon">
                        <q-icon name="title" />
                      </div>
                      Title Suggestions
                    </div>
                    <div class="suggestion-list">
                      <div
                        v-for="(titleOption, index) in titleOptions"
                        :key="'title-' + index"
                        class="suggestion-item"
                        :class="{ selected: title === titleOption.value }"
                        @click="title = titleOption.value"
                      >
                        <div class="suggestion-content">
                          <div class="suggestion-text">{{ titleOption.label }}</div>
                          <q-icon name="check" class="check-icon" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Subtitle Suggestions Column -->
                  <div class="suggestion-column">
                    <div class="column-header">
                      <div class="column-icon">
                        <q-icon name="subtitles" />
                      </div>
                      Subtitle Suggestions
                    </div>
                    <div class="suggestion-list">
                      <div
                        v-for="(subtitleOption, index) in subtitleOptions"
                        :key="'subtitle-' + index"
                        class="suggestion-item"
                        :class="{ selected: subtitle === subtitleOption.value }"
                        @click="subtitle = subtitleOption.value"
                      >
                        <div class="suggestion-content">
                          <div class="suggestion-text">{{ subtitleOption.label }}</div>
                          <q-icon name="check" class="check-icon" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';

const props = withDefaults(
  defineProps<{
    titleModelValue: string;
    subtitleModelValue: string;
    mission: string;
    transformations?: string;
    coreNeeds?: string;
    updating?: boolean;
  }>(),
  {
    updating: false,
  },
);

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);
const $q = useQuasar();

// Component state
const loading = ref(false);
const suggestions = ref({
  titles: [] as string[],
  subtitles: [] as string[],
});
const results = ref(''); // Store previous results to avoid repetition

// Computed properties
const canGenerate = computed(() => {
  return props.mission ? true : props.coreNeeds && props.transformations;
});

const titleOptions = computed(() => {
  return suggestions.value.titles.map((title, index) => ({
    label: title,
    value: title,
    index,
  }));
});

const subtitleOptions = computed(() => {
  return suggestions.value.subtitles.map((subtitle, index) => ({
    label: subtitle,
    value: subtitle,
    index,
  }));
});

// AI prompt for combined title-subtitle generation
const combinedPrompt = computed(() => {
  let promptStr = `Generate 5 title and subtitle pairs for a non-fiction book.

Mission Statement: "${props.mission}"`;

  if (props.coreNeeds) {
    promptStr += `\nCore Needs: "${props.coreNeeds}"`;
  }

  if (props.transformations) {
    promptStr += `\nTransformations: "${props.transformations}"`;
  }

  promptStr += `

Requirements:
1. Create exactly 5 title-subtitle pairs
2. Titles should be 1 or 3 words (never 2 words), avoiding gerunds
3. Subtitles should include promises or benefits, be compelling and original
4. Each title should be on its own line, followed by its subtitle on the next line
5. Separate each pair with a blank line
6. Use plain text without numbering, bullets, or special formatting
7. Make titles provocative and curiosity-inducing
8. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;

  return promptStr;
});

// Generate title-subtitle pairs
async function generateTitleSubtitlePairs() {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message:
        'Manny needs your mission statement and core needs or transformations to generate suggestions.',
    });
    return;
  }

  // Check if user wants to override existing content
  if (
    (title.value || subtitle.value) &&
    suggestions.value.titles.length === 0
  ) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return;
    }
  }

  let promptRequest = combinedPrompt.value;

  // Add previous results to avoid repetition
  if (results.value) {
    promptRequest += `\n\nPlease avoid repeating or being similar to these previous results:\n${results.value}`;
  }

  loading.value = true;

  try {
    const response = await composeText(promptRequest);

    // Parse the response to extract titles and subtitles
    const pairs = parseTitleSubtitleResponse(response);

    if (pairs.titles.length === 5 && pairs.subtitles.length === 5) {
      suggestions.value = pairs;
      // Store results to avoid repetition in future calls
      results.value = `${results.value}\nTitles: ${pairs.titles.join(
        ', ',
      )}\nSubtitles: ${pairs.subtitles.join(', ')}`;
    } else {
      throw new Error('Unexpected response format from AI');
    }
  } catch (e) {
    console.error(e);
    $q.notify({
      type: 'negative',
      message:
        'An error occurred while generating suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

// Parse AI response to extract title-subtitle pairs
function parseTitleSubtitleResponse(response: string): {
  titles: string[];
  subtitles: string[];
} {
  const titles: string[] = [];
  const subtitles: string[] = [];

  // Split by double newlines to get pairs, then by single newlines within pairs
  const lines = response
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      const title = lines[i]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();
      const subtitle = lines[i + 1]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();

      if (title && subtitle) {
        titles.push(title);
        subtitles.push(subtitle);
      }
    }
  }

  return { titles, subtitles };
}

// Initialize component
onMounted(async () => {
  // If there are existing values, don't auto-generate
  if (title.value || subtitle.value) {
    // Could optionally generate suggestions in background
  }
});
</script>

<!-- Styles are now in global onboarding-common.scss -->
