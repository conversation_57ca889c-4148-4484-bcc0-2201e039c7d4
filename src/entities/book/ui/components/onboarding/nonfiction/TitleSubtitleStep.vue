<template>
  <OnboardingStepWrapper
    theme="default"
    content-title="Create Your Non-Fiction Title & Subtitle"
    content-subtitle="Craft a compelling title and subtitle that clearly communicates your book's value proposition."
    content-icon="title"
    :show-actions="false"
  >
    <!-- Title & Subtitle Input Section -->
    <OnboardingFormSection
      label="Book Title & Subtitle"
      description="Create a compelling title and subtitle that work together to communicate your book's value proposition."
      icon="edit"
    >
      <div class="form-grid grid-2">
        <OnboardingInput
          v-model="title"
          label="Book Title"
          placeholder="Enter your book title..."
          prepend-icon="title"
          :disabled="loading"
        />

        <OnboardingInput
          v-model="subtitle"
          label="Book Subtitle"
          placeholder="Enter your book subtitle..."
          prepend-icon="subtitles"
          :disabled="loading"
        />
      </div>

      <div class="form-actions">
        <MannyButton
          label="Generate Title & Subtitle Ideas"
          variant="generate"
          :loading="loading"
          :disabled="!canGenerate"
          tooltip="Ask <PERSON> for AI-powered suggestions"
          @click="generateTitleSubtitlePairs"
        />
      </div>
    </OnboardingFormSection>

    <!-- AI Suggestions Section -->
    <OnboardingFormSection
      v-if="suggestions.titles.length > 0"
      label="AI Suggestions"
      description="Choose from these AI-generated combinations or mix and match titles and subtitles."
      icon="auto_awesome"
    >
      <OnboardingSuggestionList
        :columns="[
          {
            key: 'titles',
            title: 'Title Suggestions',
            icon: 'title',
            suggestions: titleOptions
          },
          {
            key: 'subtitles',
            title: 'Subtitle Suggestions',
            icon: 'subtitles',
            suggestions: subtitleOptions
          }
        ]"
        :selected-values="{ titles: title, subtitles: subtitle }"
        @select="handleSuggestionSelect"
      />
    </OnboardingFormSection>

    <!-- Loading State -->
    <OnboardingLoadingState
      v-if="loading"
      variant="spinner"
      text="Manny is generating title and subtitle ideas..."
    />
  </OnboardingStepWrapper>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import {
  OnboardingStepWrapper,
  OnboardingFormSection,
  OnboardingInput,
  OnboardingSuggestionList,
  OnboardingLoadingState,
  MannyButton,
  type OnboardingSuggestion,
} from '../shared';

const props = withDefaults(
  defineProps<{
    titleModelValue: string;
    subtitleModelValue: string;
    mission: string;
    transformations?: string;
    coreNeeds?: string;
    updating?: boolean;
  }>(),
  {
    updating: false,
  },
);

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);
const $q = useQuasar();

// Component state
const loading = ref(false);
const suggestions = ref({
  titles: [] as string[],
  subtitles: [] as string[],
});
const results = ref(''); // Store previous results to avoid repetition

// Computed properties
const canGenerate = computed(() => {
  return props.mission ? true : props.coreNeeds && props.transformations;
});

const titleOptions = computed((): OnboardingSuggestion[] => {
  return suggestions.value.titles.map((title) => ({
    label: title,
    value: title,
  }));
});

const subtitleOptions = computed((): OnboardingSuggestion[] => {
  return suggestions.value.subtitles.map((subtitle) => ({
    label: subtitle,
    value: subtitle,
  }));
});

// Event handlers
const handleSuggestionSelect = (key: string, value: string) => {
  if (key === 'titles') {
    title.value = value;
  } else if (key === 'subtitles') {
    subtitle.value = value;
  }
};

// AI prompt for combined title-subtitle generation
const combinedPrompt = computed(() => {
  let promptStr = `Generate 5 title and subtitle pairs for a non-fiction book.

Mission Statement: "${props.mission}"`;

  if (props.coreNeeds) {
    promptStr += `\nCore Needs: "${props.coreNeeds}"`;
  }

  if (props.transformations) {
    promptStr += `\nTransformations: "${props.transformations}"`;
  }

  promptStr += `

Requirements:
1. Create exactly 5 title-subtitle pairs
2. Titles should be 1 or 3 words (never 2 words), avoiding gerunds
3. Subtitles should include promises or benefits, be compelling and original
4. Each title should be on its own line, followed by its subtitle on the next line
5. Separate each pair with a blank line
6. Use plain text without numbering, bullets, or special formatting
7. Make titles provocative and curiosity-inducing
8. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;

  return promptStr;
});

// Generate title-subtitle pairs
async function generateTitleSubtitlePairs() {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message:
        'Manny needs your mission statement and core needs or transformations to generate suggestions.',
    });
    return;
  }

  // Check if user wants to override existing content
  if (
    (title.value || subtitle.value) &&
    suggestions.value.titles.length === 0
  ) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return;
    }
  }

  let promptRequest = combinedPrompt.value;

  // Add previous results to avoid repetition
  if (results.value) {
    promptRequest += `\n\nPlease avoid repeating or being similar to these previous results:\n${results.value}`;
  }

  loading.value = true;

  try {
    const response = await composeText(promptRequest);

    // Parse the response to extract titles and subtitles
    const pairs = parseTitleSubtitleResponse(response);

    if (pairs.titles.length === 5 && pairs.subtitles.length === 5) {
      suggestions.value = pairs;
      // Store results to avoid repetition in future calls
      results.value = `${results.value}\nTitles: ${pairs.titles.join(
        ', ',
      )}\nSubtitles: ${pairs.subtitles.join(', ')}`;
    } else {
      throw new Error('Unexpected response format from AI');
    }
  } catch (e) {
    console.error(e);
    $q.notify({
      type: 'negative',
      message:
        'An error occurred while generating suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

// Parse AI response to extract title-subtitle pairs
function parseTitleSubtitleResponse(response: string): {
  titles: string[];
  subtitles: string[];
} {
  const titles: string[] = [];
  const subtitles: string[] = [];

  // Split by double newlines to get pairs, then by single newlines within pairs
  const lines = response
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      const title = lines[i]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();
      const subtitle = lines[i + 1]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();

      if (title && subtitle) {
        titles.push(title);
        subtitles.push(subtitle);
      }
    }
  }

  return { titles, subtitles };
}

// Initialize component
onMounted(async () => {
  // If there are existing values, don't auto-generate
  if (title.value || subtitle.value) {
    // Could optionally generate suggestions in background
  }
});
</script>

<style scoped>
.title-subtitle-step {
  max-width: 100%;
}

.q-list .q-item {
  min-height: 56px;
}

.q-item-label {
  white-space: normal;
  word-break: break-word;
}

@media (max-width: 768px) {
  .row.q-gutter-md > .col {
    margin-bottom: 1rem;
  }
}
</style>
